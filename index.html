<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>CSS Magic Tricks - 10 Techniques That Will Blow Your Mind</title>
    <meta
      name="description"
      content="Interactive demonstrations of 10 advanced CSS techniques with neon cyberpunk theme. Explore Container Queries, CSS Houdini, Grid Magic and more!" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta
      property="og:url"
      content="https://css-magic-tricks-examples.netlify.app/" />
    <meta
      property="og:title"
      content="CSS Magic Tricks - 10 Techniques That Will Blow Your Mind" />
    <meta
      property="og:description"
      content="Interactive demonstrations of 10 advanced CSS techniques with neon cyberpunk theme" />
    <meta property="og:image" content="./assets/images/og-preview.jpg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta
      property="twitter:url"
      content="https://css-magic-tricks-examples.netlify.app/" />
    <meta
      property="twitter:title"
      content="CSS Magic Tricks - 10 Techniques That Will Blow Your Mind" />
    <meta
      property="twitter:description"
      content="Interactive demonstrations of 10 advanced CSS techniques with neon cyberpunk theme" />
    <meta property="twitter:image" content="./assets/images/og-preview.jpg" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="./assets/images/favicon.ico" />

    <!-- CSS Files -->
    <link rel="stylesheet" href="./assets/css/main.css" />
    <link rel="stylesheet" href="./assets/css/neon-theme.css" />

    <!-- External Dependencies -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css"
      rel="stylesheet" />
  </head>
  <body>
    <!-- Placeholder para o conteúdo principal -->
    <div id="app">
      <header class="hero-header">
        <h1 class="neon-title">CSS Magic Tricks</h1>
        <p class="hero-description">10 Técnicas Que Vão Te Surpreender</p>
      </header>

      <main class="cards-grid">
        <!-- Cards serão implementados na próxima task -->
      </main>

      <footer class="site-footer">
        <p>&copy; 2024 CSS Magic Tricks - Blueprint Blog</p>
      </footer>
    </div>

    <!-- JavaScript Files -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.11/clipboard.min.js"></script>
    <script src="./assets/js/main.js"></script>
    <script src="./assets/js/code-toggle.js"></script>
    <script src="./assets/js/copy-clipboard.js"></script>
  </body>
</html>
