<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>CSS Magic Tricks - 10 Techniques That Will Blow Your Mind</title>
    <meta
      name="description"
      content="Interactive demonstrations of 10 advanced CSS techniques with neon cyberpunk theme. Explore Container Queries, CSS Houdini, Grid Magic and more!" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta
      property="og:url"
      content="https://css-magic-tricks-examples.netlify.app/" />
    <meta
      property="og:title"
      content="CSS Magic Tricks - 10 Techniques That Will Blow Your Mind" />
    <meta
      property="og:description"
      content="Interactive demonstrations of 10 advanced CSS techniques with neon cyberpunk theme" />
    <meta property="og:image" content="./assets/images/og-preview.jpg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta
      property="twitter:url"
      content="https://css-magic-tricks-examples.netlify.app/" />
    <meta
      property="twitter:title"
      content="CSS Magic Tricks - 10 Techniques That Will Blow Your Mind" />
    <meta
      property="twitter:description"
      content="Interactive demonstrations of 10 advanced CSS techniques with neon cyberpunk theme" />
    <meta property="twitter:image" content="./assets/images/og-preview.jpg" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="./assets/images/favicon.ico" />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&display=swap"
      rel="stylesheet" />

    <!-- CSS Files -->
    <link rel="stylesheet" href="./assets/css/main.css" />
    <link rel="stylesheet" href="./assets/css/neon-theme.css" />
    <link rel="stylesheet" href="./assets/css/tricks/container-queries.css" />
    <link rel="stylesheet" href="./assets/css/tricks/tooltips.css" />
    <link rel="stylesheet" href="./assets/css/tricks/hover-effects.css" />

    <!-- External Dependencies -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css"
      rel="stylesheet" />
  </head>
  <body>
    <!-- Main content placeholder -->
    <div id="app">
      <header class="hero-header">
        <h1 class="neon-title" data-text="CSS Magic Tricks">CSS Magic Tricks</h1>
        <p class="hero-description">10 Techniques That Will Blow Your Mind</p>
      </header>

      <main class="cards-grid">
        <!-- Card 1: Container Queries -->
        <div class="magic-card" data-trick="1">
          <div class="card-header">
            <h3 class="trick-title">Container Queries</h3>
            <span class="trick-number">01</span>
          </div>

          <div class="card-demo">
            <p class="demo-description">
              Responsive card that changes layout based on container size
            </p>
            <div class="container-queries-demo">
              <div class="demo-instructions">
                <p>🔄 <strong>Drag the right edge</strong> to resize the container and watch the layout adapt!</p>
              </div>

              <div class="resizable-container" id="container-demo">
                <div class="size-indicator" id="size-display">400px</div>

                <div class="responsive-card">
                  <div class="card-header-demo">
                    <div class="card-avatar">JD</div>
                    <div class="card-info">
                      <h4 class="card-title">John Doe</h4>
                      <p class="card-subtitle">Frontend Developer</p>
                    </div>
                  </div>

                  <div class="card-content">
                    This card layout changes based on the container size using CSS Container Queries.
                    Try resizing to see different layouts!
                  </div>

                  <div class="card-actions">
                    <button class="card-button">Follow</button>
                    <button class="card-button">Message</button>
                  </div>
                </div>
              </div>

              <div class="breakpoint-indicators">
                <div class="breakpoint small" data-size="< 350px">
                  <span class="indicator-dot"></span>
                  <span class="indicator-label">Small</span>
                </div>
                <div class="breakpoint medium" data-size="350-549px">
                  <span class="indicator-dot"></span>
                  <span class="indicator-label">Medium</span>
                </div>
                <div class="breakpoint large" data-size="550px+">
                  <span class="indicator-dot"></span>
                  <span class="indicator-label">Large</span>
                </div>
              </div>
            </div>
          </div>

          <div class="card-controls">
            <button
              class="btn-show-code"
              data-target="code-1"
              aria-expanded="false">
              <span class="btn-text">Show Code</span>
              <span class="btn-icon">⚡</span>
            </button>
          </div>

          <div class="code-panel" id="code-1" hidden>
            <div class="code-header">
              <span class="code-label">CSS Code</span>
              <button class="btn-copy" data-clipboard-target="code-content-1">
                Copy
              </button>
            </div>
            <pre class="code-content" id="code-content-1">
              <code class="language-css">/* Container Queries Demo */
.resizable-container {
  container-type: inline-size;
  container-name: demo-container;
  resize: horizontal;
  overflow: auto;
}

/* Small container (< 350px) */
@container demo-container (max-width: 349px) {
  .card-header-demo {
    flex-direction: column;
    text-align: center;
  }
  .card-actions {
    flex-direction: column;
  }
}

/* Medium container (350px - 549px) */
@container demo-container (min-width: 350px) and (max-width: 549px) {
  .card-header-demo {
    flex-direction: row;
  }
}

/* Large container (550px+) */
@container demo-container (min-width: 550px) {
  .responsive-card {
    display: grid;
    grid-template-columns: auto 1fr auto;
    grid-template-areas:
      "avatar info actions"
      "content content content";
  }
}</code>
            </pre>
          </div>
        </div>

        <!-- Card 2: CSS Tooltips -->
        <div class="magic-card" data-trick="2">
          <div class="card-header">
            <h3 class="trick-title">CSS Tooltips</h3>
            <span class="trick-number">02</span>
          </div>

          <div class="card-demo">
            <p class="demo-description">
              Multiple tooltip examples with different positions
            </p>
            <div class="tooltips-demo">
              <div class="tooltip-instructions">
                <p>🖱️ <strong>Hover over the buttons</strong> to see different tooltip positions and effects!</p>
              </div>

              <div class="tooltip-grid">
                <!-- Position Tooltips -->
                <div class="tooltip-section">
                  <h4 class="tooltip-section-title">Positions</h4>
                  <p class="tooltip-section-description">Basic tooltip positioning</p>

                  <div class="tooltip tooltip-top" data-tooltip="I'm on top! 🔝">
                    <button class="tooltip-btn">Top</button>
                  </div>

                  <div class="tooltip tooltip-bottom" data-tooltip="I'm at the bottom! 🔽">
                    <button class="tooltip-btn">Bottom</button>
                  </div>

                  <div style="display: flex; gap: 1rem;">
                    <div class="tooltip tooltip-left" data-tooltip="Left side! ⬅️">
                      <button class="tooltip-btn">Left</button>
                    </div>

                    <div class="tooltip tooltip-right" data-tooltip="Right side! ➡️">
                      <button class="tooltip-btn">Right</button>
                    </div>
                  </div>
                </div>

                <!-- Timing Tooltips -->
                <div class="tooltip-section">
                  <h4 class="tooltip-section-title">Timing</h4>
                  <p class="tooltip-section-description">Different delay timings</p>

                  <div class="tooltip tooltip-top" data-tooltip="Instant tooltip! ⚡">
                    <button class="tooltip-btn">Instant</button>
                  </div>

                  <div class="tooltip tooltip-top tooltip-delay" data-tooltip="0.5s delay ⏱️">
                    <button class="tooltip-btn">Delayed</button>
                  </div>

                  <div class="tooltip tooltip-top tooltip-delay-long" data-tooltip="1s delay ⏰">
                    <button class="tooltip-btn">Long Delay</button>
                  </div>
                </div>

                <!-- Animation Effects -->
                <div class="tooltip-section">
                  <h4 class="tooltip-section-title">Animations</h4>
                  <p class="tooltip-section-description">Special animation effects</p>

                  <div class="tooltip tooltip-top tooltip-bounce" data-tooltip="Bouncy tooltip! 🏀">
                    <button class="tooltip-btn">Bounce</button>
                  </div>

                  <div class="tooltip tooltip-top tooltip-slide" data-tooltip="Smooth slide! 🛝">
                    <button class="tooltip-btn">Slide</button>
                  </div>

                  <div class="tooltip tooltip-top tooltip-fade" data-tooltip="Slow fade! 🌅">
                    <button class="tooltip-btn">Fade</button>
                  </div>
                </div>

                <!-- Special Effects -->
                <div class="tooltip-section">
                  <h4 class="tooltip-section-title">Effects</h4>
                  <p class="tooltip-section-description">Visual enhancements</p>

                  <div class="tooltip tooltip-top tooltip-glow" data-tooltip="Glowing tooltip! ✨">
                    <button class="tooltip-btn">Glow</button>
                  </div>

                  <div class="tooltip tooltip-top tooltip-rainbow" data-tooltip="Rainbow colors! 🌈">
                    <button class="tooltip-btn">Rainbow</button>
                  </div>

                  <div class="tooltip tooltip-top tooltip-multiline" data-tooltip="This is a multiline tooltip!&#10;It can show multiple lines&#10;of information! 📝">
                    <button class="tooltip-btn">Multiline</button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card-controls">
            <button
              class="btn-show-code"
              data-target="code-2"
              aria-expanded="false">
              <span class="btn-text">Show Code</span>
              <span class="btn-icon">⚡</span>
            </button>
          </div>

          <div class="code-panel" id="code-2" hidden>
            <div class="code-header">
              <span class="code-label">CSS Code</span>
              <button class="btn-copy" data-clipboard-target="code-content-2">
                Copy
              </button>
            </div>
            <pre class="code-content" id="code-content-2">
              <code class="language-css">/* CSS Tooltips - Advanced Examples */

/* Base Tooltip */
.tooltip {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  background: linear-gradient(135deg, #0a0a0a, #1a1a2e);
  color: #ffffff;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-size: 0.8rem;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
  border: 1px solid #00ffff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* Top Position */
.tooltip-top::after {
  bottom: calc(100% + 8px);
  left: 50%;
  transform: translateX(-50%) translateY(10px);
}

.tooltip-top:hover::after {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

/* Delayed Tooltip */
.tooltip-delay::after {
  transition-delay: 0.5s;
}

/* Bounce Animation */
.tooltip-bounce:hover::after {
  animation: tooltipBounce 0.6s ease;
}

@keyframes tooltipBounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40%, 43% {
    transform: translateX(-50%) translateY(-10px);
  }
}</code>
            </pre>
          </div>
        </div>

        <!-- Card 3: Magic Hover Effects -->
        <div class="magic-card" data-trick="3">
          <div class="card-header">
            <h3 class="trick-title">Magic Hover Effects</h3>
            <span class="trick-number">03</span>
          </div>

          <div class="card-demo">
            <p class="demo-description">
              Collection of advanced button hover effects
            </p>
            <div class="hover-effects-demo">
              <div class="hover-instructions">
                <p>🎨 <strong>Hover over the buttons</strong> to see amazing transformation effects!</p>
              </div>

              <div class="effects-grid">
                <!-- Morphing Effects -->
                <div class="effect-section">
                  <h4 class="effect-section-title">Morphing</h4>
                  <p class="effect-section-description">Shape and form transformations</p>

                  <button class="effect-btn liquid-btn">
                    Liquid Morph
                  </button>

                  <button class="effect-btn ripple-btn">
                    Ripple Wave
                  </button>
                </div>

                <!-- 3D Effects -->
                <div class="effect-section">
                  <h4 class="effect-section-title">3D Transform</h4>
                  <p class="effect-section-description">Three-dimensional animations</p>

                  <button class="effect-btn flip-btn">
                    <span>3D Flip</span>
                  </button>

                  <button class="effect-btn magnetic-btn">
                    Magnetic Lift
                  </button>
                </div>

                <!-- Special Effects -->
                <div class="effect-section">
                  <h4 class="effect-section-title">Special FX</h4>
                  <p class="effect-section-description">Unique visual effects</p>

                  <button class="effect-btn glitch-btn" data-text="GLITCH">
                    Glitch
                  </button>

                  <button class="effect-btn neon-btn">
                    Neon Glow
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div class="card-controls">
            <button
              class="btn-show-code"
              data-target="code-3"
              aria-expanded="false">
              <span class="btn-text">Show Code</span>
              <span class="btn-icon">⚡</span>
            </button>
          </div>

          <div class="code-panel" id="code-3" hidden>
            <div class="code-header">
              <span class="code-label">CSS Code</span>
              <button class="btn-copy" data-clipboard-target="code-content-3">
                Copy
              </button>
            </div>
            <pre class="code-content" id="code-content-3">
              <code class="language-css">/* Magic Hover Effects - Advanced Examples */

/* Liquid Morphing Effect */
.liquid-btn {
  background: linear-gradient(45deg, #00ffff, #8b5cf6);
  border-radius: 50px;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.320, 1);
}

.liquid-btn::before {
  content: '';
  position: absolute;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background: linear-gradient(45deg, #ff0080, #00ff41);
  border-radius: 50px;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.320, 1);
  z-index: -1;
}

.liquid-btn:hover {
  transform: scale(1.1) rotate(2deg);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 255, 255, 0.4);
}

.liquid-btn:hover::before {
  opacity: 1;
  transform: scale(1.2) rotate(-2deg);
  border-radius: 30px;
}

/* 3D Flip Effect */
.flip-btn {
  perspective: 1000px;
  transform-style: preserve-3d;
  transition: transform 0.6s ease;
}

.flip-btn:hover {
  transform: rotateY(180deg);
}

/* Glitch Effect */
.glitch-btn:hover {
  animation: glitchMain 0.3s ease-in-out infinite;
}

@keyframes glitchMain {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(-1px, 1px); }
  40% { transform: translate(1px, -1px); }
  60% { transform: translate(-1px, -1px); }
  80% { transform: translate(1px, 1px); }
}</code>
            </pre>
          </div>
        </div>

        <!-- Card 4: Fluid Typography -->
        <div class="magic-card" data-trick="4">
          <div class="card-header">
            <h3 class="trick-title">Fluid Typography</h3>
            <span class="trick-number">04</span>
          </div>

          <div class="card-demo">
            <p class="demo-description">
              Text that scales smoothly with viewport
            </p>
            <div class="fluid-typography-demo">
              <!-- Demo will be implemented in Card 4 task -->
              <div class="placeholder-demo">
                <h2 class="fluid-text">Responsive Text</h2>
                <p>clamp() function visualization coming soon...</p>
              </div>
            </div>
          </div>

          <div class="card-controls">
            <button
              class="btn-show-code"
              data-target="code-4"
              aria-expanded="false">
              <span class="btn-text">Show Code</span>
              <span class="btn-icon">⚡</span>
            </button>
          </div>

          <div class="code-panel" id="code-4" hidden>
            <div class="code-header">
              <span class="code-label">CSS Code</span>
              <button class="btn-copy" data-clipboard-target="code-content-4">
                Copy
              </button>
            </div>
            <pre class="code-content" id="code-content-4">
              <code class="language-css">/* Fluid Typography Example */
.fluid-text {
  font-size: clamp(1.5rem, 4vw, 3rem);
  line-height: clamp(1.2, 1.5vw, 1.4);
}

/* Alternative approach */
.responsive-text {
  font-size: calc(1rem + 2vw);
  max-font-size: 2.5rem;
  min-font-size: 1rem;
}</code>
            </pre>
          </div>
        </div>

        <!-- Card 5: Scroll Snap -->
        <div class="magic-card" data-trick="5">
          <div class="card-header">
            <h3 class="trick-title">Scroll Snap</h3>
            <span class="trick-number">05</span>
          </div>

          <div class="card-demo">
            <p class="demo-description">
              Horizontal scrolling gallery with snap points
            </p>
            <div class="scroll-snap-demo">
              <!-- Demo will be implemented in Card 5 task -->
              <div class="placeholder-demo">
                <div class="scroll-container">
                  <div class="scroll-item">Item 1</div>
                  <div class="scroll-item">Item 2</div>
                  <div class="scroll-item">Item 3</div>
                </div>
                <p>Smooth section navigation coming soon...</p>
              </div>
            </div>
          </div>

          <div class="card-controls">
            <button
              class="btn-show-code"
              data-target="code-5"
              aria-expanded="false">
              <span class="btn-text">Show Code</span>
              <span class="btn-icon">⚡</span>
            </button>
          </div>

          <div class="code-panel" id="code-5" hidden>
            <div class="code-header">
              <span class="code-label">CSS Code</span>
              <button class="btn-copy" data-clipboard-target="code-content-5">
                Copy
              </button>
            </div>
            <pre class="code-content" id="code-content-5">
              <code class="language-css">/* Scroll Snap Example */
.scroll-container {
  display: flex;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  gap: 1rem;
}

.scroll-item {
  flex: 0 0 300px;
  scroll-snap-align: start;
  scroll-snap-stop: always;
}</code>
            </pre>
          </div>
        </div>

        <!-- Card 6: Dynamic Themes -->
        <div class="magic-card" data-trick="6">
          <div class="card-header">
            <h3 class="trick-title">Dynamic Themes</h3>
            <span class="trick-number">06</span>
          </div>

          <div class="card-demo">
            <p class="demo-description">Theme toggle between light/dark/neon modes</p>
            <div class="theme-system-demo">
              <!-- Demo will be implemented in Card 6 task -->
              <div class="placeholder-demo">
                <button class="theme-toggle">🌙 Dark</button>
                <button class="theme-toggle">☀️ Light</button>
                <button class="theme-toggle">⚡ Neon</button>
                <p>CSS custom properties in action coming soon...</p>
              </div>
            </div>
          </div>

          <div class="card-controls">
            <button class="btn-show-code" data-target="code-6" aria-expanded="false">
              <span class="btn-text">Show Code</span>
              <span class="btn-icon">⚡</span>
            </button>
          </div>

          <div class="code-panel" id="code-6" hidden>
            <div class="code-header">
              <span class="code-label">CSS Code</span>
              <button class="btn-copy" data-clipboard-target="code-content-6">
                Copy
              </button>
            </div>
            <pre class="code-content" id="code-content-6">
              <code class="language-css">/* Dynamic Themes Example */
:root {
  --bg-color: #ffffff;
  --text-color: #000000;
}

[data-theme="dark"] {
  --bg-color: #1a1a1a;
  --text-color: #ffffff;
}

[data-theme="neon"] {
  --bg-color: #0a0a0a;
  --text-color: #00ffff;
}

body {
  background: var(--bg-color);
  color: var(--text-color);
  transition: all 0.3s ease;
}</code>
            </pre>
          </div>
        </div>

        <!-- Card 7: CSS Grid Magic -->
        <div class="magic-card" data-trick="7">
          <div class="card-header">
            <h3 class="trick-title">CSS Grid Magic</h3>
            <span class="trick-number">07</span>
          </div>

          <div class="card-demo">
            <p class="demo-description">Complex grid layout that adapts responsively</p>
            <div class="grid-layouts-demo">
              <!-- Demo will be implemented in Card 7 task -->
              <div class="placeholder-demo">
                <div class="grid-container">
                  <div class="grid-item">Header</div>
                  <div class="grid-item">Sidebar</div>
                  <div class="grid-item">Main</div>
                  <div class="grid-item">Footer</div>
                </div>
                <p>Holy grail layout coming soon...</p>
              </div>
            </div>
          </div>

          <div class="card-controls">
            <button class="btn-show-code" data-target="code-7" aria-expanded="false">
              <span class="btn-text">Show Code</span>
              <span class="btn-icon">⚡</span>
            </button>
          </div>

          <div class="code-panel" id="code-7" hidden>
            <div class="code-header">
              <span class="code-label">CSS Code</span>
              <button class="btn-copy" data-clipboard-target="code-content-7">
                Copy
              </button>
            </div>
            <pre class="code-content" id="code-content-7">
              <code class="language-css">/* CSS Grid Magic Example */
.grid-container {
  display: grid;
  grid-template-areas:
    "header header"
    "sidebar main"
    "footer footer";
  grid-template-rows: auto 1fr auto;
  grid-template-columns: 200px 1fr;
  min-height: 100vh;
}

.header { grid-area: header; }
.sidebar { grid-area: sidebar; }
.main { grid-area: main; }
.footer { grid-area: footer; }</code>
            </pre>
          </div>
        </div>

        <!-- Card 8: Physics Animations -->
        <div class="magic-card" data-trick="8">
          <div class="card-header">
            <h3 class="trick-title">Physics Animations</h3>
            <span class="trick-number">08</span>
          </div>

          <div class="card-demo">
            <p class="demo-description">Collection of natural motion animations</p>
            <div class="animations-demo">
              <!-- Demo will be implemented in Card 8 task -->
              <div class="placeholder-demo">
                <button class="physics-button">Bounce</button>
                <button class="physics-button">Spring</button>
                <button class="physics-button">Elastic</button>
                <p>Custom easing functions coming soon...</p>
              </div>
            </div>
          </div>

          <div class="card-controls">
            <button class="btn-show-code" data-target="code-8" aria-expanded="false">
              <span class="btn-text">Show Code</span>
              <span class="btn-icon">⚡</span>
            </button>
          </div>

          <div class="code-panel" id="code-8" hidden>
            <div class="code-header">
              <span class="code-label">CSS Code</span>
              <button class="btn-copy" data-clipboard-target="code-content-8">
                Copy
              </button>
            </div>
            <pre class="code-content" id="code-content-8">
              <code class="language-css">/* Physics Animations Example */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

.bounce {
  animation: bounce 1s ease-in-out;
}</code>
            </pre>
          </div>
        </div>

        <!-- Card 9: Advanced Selectors -->
        <div class="magic-card" data-trick="9">
          <div class="card-header">
            <h3 class="trick-title">Advanced Selectors</h3>
            <span class="trick-number">09</span>
          </div>

          <div class="card-demo">
            <p class="demo-description">Form with smart validation styling</p>
            <div class="advanced-selectors-demo">
              <!-- Demo will be implemented in Card 9 task -->
              <div class="placeholder-demo">
                <input type="email" placeholder="Enter email">
                <input type="password" placeholder="Enter password">
                <p>:has() selector in action coming soon...</p>
              </div>
            </div>
          </div>

          <div class="card-controls">
            <button class="btn-show-code" data-target="code-9" aria-expanded="false">
              <span class="btn-text">Show Code</span>
              <span class="btn-icon">⚡</span>
            </button>
          </div>

          <div class="code-panel" id="code-9" hidden>
            <div class="code-header">
              <span class="code-label">CSS Code</span>
              <button class="btn-copy" data-clipboard-target="code-content-9">
                Copy
              </button>
            </div>
            <pre class="code-content" id="code-content-9">
              <code class="language-css">/* Advanced Selectors Example */
/* Style form when it has valid inputs */
form:has(input:valid) {
  border-color: green;
}

/* Style form when it has invalid inputs */
form:has(input:invalid) {
  border-color: red;
}

/* Target elements that don't have a specific class */
div:not(.special) {
  opacity: 0.5;
}

/* Modern selector combinations */
input:is(:focus, :hover) {
  outline: 2px solid blue;
}</code>
            </pre>
          </div>
        </div>

        <!-- Card 10: CSS Houdini -->
        <div class="magic-card" data-trick="10">
          <div class="card-header">
            <h3 class="trick-title">CSS Houdini</h3>
            <span class="trick-number">10</span>
          </div>

          <div class="card-demo">
            <p class="demo-description">Custom paint worklet with adjustable properties</p>
            <div class="houdini-demo">
              <!-- Demo will be implemented in Card 10 task -->
              <div class="placeholder-demo">
                <div class="houdini-canvas">Custom Paint API</div>
                <input type="range" min="0" max="100" value="50">
                <p>Real-time paint API demonstration coming soon...</p>
              </div>
            </div>
          </div>

          <div class="card-controls">
            <button class="btn-show-code" data-target="code-10" aria-expanded="false">
              <span class="btn-text">Show Code</span>
              <span class="btn-icon">⚡</span>
            </button>
          </div>

          <div class="code-panel" id="code-10" hidden>
            <div class="code-header">
              <span class="code-label">CSS Code</span>
              <button class="btn-copy" data-clipboard-target="code-content-10">
                Copy
              </button>
            </div>
            <pre class="code-content" id="code-content-10">
              <code class="language-css">/* CSS Houdini Example */
/* Register custom property */
@property --gradient-angle {
  syntax: '<angle>';
  initial-value: 0deg;
  inherits: false;
}

/* Use custom paint worklet */
.houdini-element {
  background: paint(custom-gradient);
  --gradient-angle: 45deg;
  transition: --gradient-angle 0.3s ease;
}

.houdini-element:hover {
  --gradient-angle: 180deg;
}</code>
            </pre>
          </div>
        </div>
      </main>

      <footer class="site-footer">
        <div class="footer-content">
          <p>&copy; 2024 CSS Magic Tricks - Blueprint Blog</p>
          <div class="footer-links">
            <a href="https://github.com/blueprintblog/css-magic-tricks-examples" target="_blank" rel="noopener">
              GitHub Repository
            </a>
            <a href="https://blueprintblog.dev" target="_blank" rel="noopener">
              Blueprint Blog
            </a>
            <a href="https://medium.com/@blueprintblog" target="_blank" rel="noopener">
              Medium Article
            </a>
          </div>
        </div>
      </footer>
    </div>

    <!-- JavaScript Files -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.11/clipboard.min.js"></script>
    <script src="./assets/js/main.js"></script>
    <script src="./assets/js/code-toggle.js"></script>
    <script src="./assets/js/copy-clipboard.js"></script>
    <script src="./assets/js/container-queries.js"></script>
  </body>
</html>
