<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
  <defs>
    <linearGradient id="neonGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00ffff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ff00ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffff00;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="32" height="32" fill="#0a0a0a"/>
  <text x="16" y="22" font-family="monospace" font-size="18" font-weight="bold" text-anchor="middle" fill="url(#neonGradient)">CSS</text>
  <circle cx="8" cy="8" r="2" fill="#00ffff" opacity="0.8"/>
  <circle cx="24" cy="8" r="2" fill="#ff00ff" opacity="0.8"/>
  <circle cx="16" cy="28" r="2" fill="#ffff00" opacity="0.8"/>
</svg>
