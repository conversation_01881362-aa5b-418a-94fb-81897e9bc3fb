{"version": 2, "name": "css-magic-tricks", "alias": ["css-magic-tricks"], "public": true, "github": {"silent": true}, "build": {"env": {"NODE_ENV": "production"}}, "functions": {}, "routes": [{"src": "/sw.js", "headers": {"Service-Worker-Allowed": "/", "Cache-Control": "public, max-age=0, must-revalidate"}}, {"src": "/manifest.json", "headers": {"Content-Type": "application/manifest+json", "Cache-Control": "public, max-age=31536000, immutable"}}, {"src": "/browserconfig.xml", "headers": {"Content-Type": "application/xml", "Cache-Control": "public, max-age=31536000, immutable"}}, {"src": "/assets/css/(.*)", "headers": {"Content-Type": "text/css", "Cache-Control": "public, max-age=31536000, immutable"}}, {"src": "/assets/js/(.*)", "headers": {"Content-Type": "application/javascript", "Cache-Control": "public, max-age=31536000, immutable"}}, {"src": "/assets/images/(.*)", "headers": {"Cache-Control": "public, max-age=31536000, immutable"}}, {"src": "/(.*\\.(png|jpg|jpeg|gif|svg|webp|ico))", "headers": {"Cache-Control": "public, max-age=31536000, immutable"}}, {"src": "/(.*\\.(woff|woff2|ttf|eot))", "headers": {"Cache-Control": "public, max-age=31536000, immutable"}}, {"src": "/", "dest": "/index.html", "headers": {"Cache-Control": "public, max-age=0, must-revalidate"}}, {"src": "/(.*)", "dest": "/index.html", "headers": {"Cache-Control": "public, max-age=0, must-revalidate"}}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=(), interest-cohort=()"}]}, {"source": "/sw.js", "headers": [{"key": "Service-Worker-Allowed", "value": "/"}, {"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}, {"source": "/assets/css/(.*)", "headers": [{"key": "Content-Type", "value": "text/css; charset=utf-8"}, {"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/assets/js/(.*)", "headers": [{"key": "Content-Type", "value": "application/javascript; charset=utf-8"}, {"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/manifest.json", "headers": [{"key": "Content-Type", "value": "application/manifest+json; charset=utf-8"}, {"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "rewrites": [{"source": "/((?!api/).*)", "destination": "/index.html"}], "cleanUrls": true, "trailingSlash": false, "framework": null, "installCommand": null, "buildCommand": null, "devCommand": null, "outputDirectory": ".", "regions": ["iad1"], "env": {"NODE_ENV": "production"}}