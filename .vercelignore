# Development files
.git/
.gitignore
.env
.env.local
.env.development
.env.test
.env.production

# Documentation (keep only essential)
PROJECT.md

# Node modules (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.log

# Test files
test/
tests/
*.test.js
*.spec.js

# Build artifacts (if any)
dist/
build/
out/

# Cache directories
.cache/
.parcel-cache/

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.build

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Backup files
*.bak
*.backup
