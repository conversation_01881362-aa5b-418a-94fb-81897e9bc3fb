<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Namespace Simple</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: #fff;
            padding: 2rem;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 12px;
        }
        #output {
            background: #000;
            color: #0f0;
            padding: 1rem;
            border-radius: 6px;
            font-family: monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Simple Namespace Test</h1>
        <div id="output"></div>
    </div>

    <!-- Test scripts directly -->
    <script>
        const output = document.getElementById('output');
        
        function log(message) {
            output.textContent += message + '\n';
        }
        
        log('🚀 Starting namespace test...');
        
        // Test 1: Create namespace
        log('📦 Creating namespace...');
        window.MagicTricks = window.MagicTricks || {};
        log('✅ Namespace created: ' + typeof window.MagicTricks);
        
        // Test 2: Define a simple class
        log('🎭 Defining test class...');
        window.MagicTricks.TestClass = class TestClass {
            constructor() {
                this.name = 'TestClass';
            }
            
            getName() {
                return this.name;
            }
        };
        log('✅ TestClass defined');
        
        // Test 3: Check if class exists
        log('🔍 Checking class existence...');
        if (window.MagicTricks.TestClass) {
            log('✅ TestClass found in namespace');
            
            // Test 4: Instantiate class
            const instance = new window.MagicTricks.TestClass();
            log('✅ Instance created: ' + instance.getName());
        } else {
            log('❌ TestClass not found');
        }
        
        // Test 5: Check namespace contents
        log('📦 Namespace contents: ' + Object.keys(window.MagicTricks));
        
        log('🎉 Test completed!');
    </script>
    
    <!-- Now load the actual scripts -->
    <script>
        log('\n🔄 Loading actual scripts...');
    </script>
    
    <script src="./assets/js/error-suppressor.js"></script>
    <script src="./assets/js/scroll-snap.js"></script>
    <script src="./assets/js/theme-system.js"></script>
    <script src="./assets/js/houdini-demo.js"></script>
    
    <script>
        // Check after scripts load
        setTimeout(() => {
            log('\n📊 Final check after script loading:');
            log('🔍 MagicTricks namespace: ' + typeof window.MagicTricks);
            if (window.MagicTricks) {
                log('📦 Available classes: ' + Object.keys(window.MagicTricks));
                log('✅ ScrollSnapGallery: ' + !!window.MagicTricks.ScrollSnapGallery);
                log('✅ ThemeSystem: ' + !!window.MagicTricks.ThemeSystem);
                log('✅ HoudiniDemo: ' + !!window.MagicTricks.HoudiniDemo);
            } else {
                log('❌ MagicTricks namespace not found after script loading');
            }
            
            log('\n🔧 Error suppressor: ' + !!window.errorSuppressor);
        }, 1000);
    </script>
</body>
</html>
