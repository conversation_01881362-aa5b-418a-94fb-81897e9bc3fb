{"name": "CSS Magic Tricks - 10 Techniques That Will Blow Your Mind", "short_name": "CSS Magic Tricks", "description": "Interactive demonstrations of 10 advanced CSS techniques with neon cyberpunk theme. Explore Container Queries, CSS Houdini, Grid Magic and more!", "start_url": "/", "display": "standalone", "background_color": "#0a0a0a", "theme_color": "#00ffff", "orientation": "portrait-primary", "scope": "/", "lang": "en", "dir": "ltr", "categories": ["education", "developer", "productivity"], "screenshots": [{"src": "/assets/images/screenshot-mobile.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "CSS Magic Tricks on mobile"}, {"src": "/assets/images/screenshot-desktop.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "CSS Magic Tricks on desktop"}], "icons": [{"src": "/assets/images/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "any"}, {"src": "/assets/images/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "any"}, {"src": "/assets/images/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "any"}, {"src": "/assets/images/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "any"}, {"src": "/assets/images/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "any"}, {"src": "/assets/images/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "any maskable"}, {"src": "/assets/images/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "any"}, {"src": "/assets/images/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "any maskable"}], "shortcuts": [{"name": "Container Queries", "short_name": "Container", "description": "Learn about CSS Container Queries", "url": "/#container-queries", "icons": [{"src": "/assets/images/shortcut-container.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Fluid Typography", "short_name": "Typography", "description": "Explore fluid typography techniques", "url": "/#fluid-typography", "icons": [{"src": "/assets/images/shortcut-typography.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Hover Effects", "short_name": "Hover", "description": "Discover amazing hover effects", "url": "/#hover-effects", "icons": [{"src": "/assets/images/shortcut-hover.png", "sizes": "96x96", "type": "image/png"}]}], "related_applications": [{"platform": "web", "url": "https://blueprintblog.dev"}], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}, "handle_links": "preferred", "capture_links": "existing-client-navigate"}