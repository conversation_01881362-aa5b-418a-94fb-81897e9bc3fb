{"name": "CSS Magic Tricks - 10 Techniques That Will Blow Your Mind", "short_name": "CSS Magic Tricks", "description": "Interactive demonstrations of 10 advanced CSS techniques with neon cyberpunk theme. Explore Container Queries, CSS Houdini, Grid Magic and more!", "start_url": "/", "display": "standalone", "background_color": "#0a0a0a", "theme_color": "#00ffff", "orientation": "portrait-primary", "scope": "/", "lang": "en", "dir": "ltr", "categories": ["education", "developer", "productivity"], "icons": [{"src": "/favicon.svg", "sizes": "any", "type": "image/svg+xml", "purpose": "any maskable"}], "shortcuts": [], "related_applications": [{"platform": "web", "url": "https://blueprintblog.dev"}], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}, "handle_links": "preferred", "capture_links": "existing-client-navigate"}