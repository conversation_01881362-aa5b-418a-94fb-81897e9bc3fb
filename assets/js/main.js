/* ===================================
   CSS MAGIC TRICKS - MAIN JAVASCRIPT
   ================================= */

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function () {
  console.log('🎨 CSS Magic Tricks initialized!');

  // Initialize all components
  initializeCodeToggle();
  initializeClipboard();
  initializeAccessibility();
  initializePerformanceOptimizations();
  initializeInteractiveFeatures();
  initializeSyntaxHighlighting();

  // Show initialization complete message
  setTimeout(() => {
    console.log('✨ All features loaded successfully!');
  }, 1000);
});

// Initialize code toggle functionality
function initializeCodeToggle() {
  // Code toggle is now handled by the CodeToggle class in code-toggle.js
  // This function is kept for compatibility but delegates to the class
  console.log('🎨 Code toggle system initialized via CodeToggle class');
}

// Initialize clipboard functionality
function initializeClipboard() {
  // Clipboard functionality is now handled by the ClipboardManager class in copy-clipboard.js
  // This function is kept for compatibility but delegates to the class
  console.log('📋 Clipboard system initialized via ClipboardManager class');
}

// Copy to clipboard functionality is now handled by ClipboardManager class

// Initialize accessibility features
function initializeAccessibility() {
  // Accessibility features are now handled by the CodeToggle class
  // This includes keyboard navigation, focus management, and screen reader support
  console.log('♿ Accessibility features initialized via CodeToggle class');

  // Add focus management for general elements
  const focusableElements = document.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  );

  focusableElements.forEach((element) => {
    element.addEventListener('focus', function () {
      this.style.outline = '2px solid var(--neon-cyan)';
      this.style.outlineOffset = '2px';
    });

    element.addEventListener('blur', function () {
      this.style.outline = '';
      this.style.outlineOffset = '';
    });
  });
}

// Performance optimizations
function initializePerformanceOptimizations() {
  // Intersection Observer for lazy loading animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px',
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animate-in');
        observer.unobserve(entry.target);
      }
    });
  }, observerOptions);

  // Observe all cards
  const cards = document.querySelectorAll('.magic-card');
  cards.forEach((card) => {
    observer.observe(card);
  });

  // Reduce motion for users who prefer it
  if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    document.documentElement.style.setProperty(
      '--animation-duration',
      '0.01ms'
    );
  }
}

// Screen reader announcements
function announceToScreenReader(message) {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', 'polite');
  announcement.setAttribute('aria-atomic', 'true');
  announcement.className = 'sr-only';
  announcement.textContent = message;

  document.body.appendChild(announcement);

  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
}

// Utility function to debounce events
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Initialize interactive features
function initializeInteractiveFeatures() {
  // Add smooth scrolling for internal links
  document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      }
    });
  });

  // Add card hover performance optimization
  const cards = document.querySelectorAll('.magic-card');
  cards.forEach((card) => {
    card.addEventListener('mouseenter', function () {
      this.style.willChange = 'transform';
    });

    card.addEventListener('mouseleave', function () {
      this.style.willChange = 'auto';
    });
  });

  // Add loading states for buttons
  const buttons = document.querySelectorAll('.btn-show-code, .btn-copy');
  buttons.forEach((button) => {
    button.addEventListener('click', function () {
      this.classList.add('loading');
      setTimeout(() => {
        this.classList.remove('loading');
      }, 300);
    });
  });

  // Add keyboard shortcuts
  document.addEventListener('keydown', function (e) {
    // Ctrl/Cmd + K for future search functionality
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
      e.preventDefault();
      console.log('Search shortcut triggered');
    }
  });
}

// Initialize syntax highlighting
function initializeSyntaxHighlighting() {
  // Check if Prism is loaded
  if (typeof Prism !== 'undefined') {
    // Highlight all code blocks
    Prism.highlightAll();
    console.log('✅ Syntax highlighting initialized');
  } else {
    console.warn('⚠️ Prism.js not loaded - syntax highlighting disabled');
  }
}

// Enhanced error handling
function handleError(error, context = 'Unknown') {
  console.error(`Error in ${context}:`, error);

  // Show user-friendly error message
  const errorDiv = document.createElement('div');
  errorDiv.className = 'error-toast';
  errorDiv.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--neon-pink);
    color: var(--text-primary);
    padding: 1rem;
    border-radius: 8px;
    z-index: 10000;
    animation: fadeInUp 0.3s ease;
  `;
  errorDiv.textContent = `Something went wrong. Please try again.`;

  document.body.appendChild(errorDiv);

  setTimeout(() => {
    errorDiv.remove();
  }, 5000);
}

// Add global error handlers
window.addEventListener('error', (e) => {
  handleError(e.error, 'Global');
});

window.addEventListener('unhandledrejection', (e) => {
  handleError(e.reason, 'Promise');
});

// Export functions for use in other modules
window.MagicTricks = {
  announceToScreenReader,
  debounce,
  handleError,
  initializeInteractiveFeatures,
  initializeSyntaxHighlighting,
};
