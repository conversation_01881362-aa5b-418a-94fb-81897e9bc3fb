/* ===================================
   CSS MAGIC TRICKS - MAIN JAVASCRIPT
   ================================= */

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
  console.log('🎨 CSS Magic Tricks initialized!');
  
  // Initialize all components
  initializeCodeToggle();
  initializeClipboard();
  initializeAccessibility();
  initializePerformanceOptimizations();
});

// Initialize code toggle functionality
function initializeCodeToggle() {
  const toggleButtons = document.querySelectorAll('.btn-show-code');
  
  toggleButtons.forEach(button => {
    button.addEventListener('click', function() {
      const targetId = this.getAttribute('data-target');
      const panel = document.getElementById(targetId);
      const buttonText = this.querySelector('.btn-text');
      
      if (panel) {
        toggleCodePanel(panel, buttonText);
      }
    });
  });
}

// Toggle code panel visibility
function toggleCodePanel(panel, buttonText) {
  const isHidden = panel.hasAttribute('hidden');
  
  if (isHidden) {
    // Show panel
    panel.removeAttribute('hidden');
    panel.style.animation = 'slideDown 0.3s ease-out';
    buttonText.textContent = 'Hide Code';
    
    // Announce to screen readers
    announceToScreenReader('Code panel opened');
  } else {
    // Hide panel
    panel.style.animation = 'slideUp 0.3s ease-out';
    buttonText.textContent = 'Show Code';
    
    setTimeout(() => {
      panel.setAttribute('hidden', '');
    }, 300);
    
    // Announce to screen readers
    announceToScreenReader('Code panel closed');
  }
}

// Initialize clipboard functionality
function initializeClipboard() {
  const copyButtons = document.querySelectorAll('.btn-copy');
  
  copyButtons.forEach(button => {
    button.addEventListener('click', function() {
      const targetId = this.getAttribute('data-clipboard-target');
      const codeElement = document.getElementById(targetId);
      
      if (codeElement) {
        copyToClipboard(codeElement.textContent, this);
      }
    });
  });
}

// Copy to clipboard functionality
function copyToClipboard(text, button) {
  if (navigator.clipboard && window.isSecureContext) {
    // Use modern clipboard API
    navigator.clipboard.writeText(text).then(() => {
      showCopyFeedback(button);
    }).catch(err => {
      console.error('Failed to copy text: ', err);
      fallbackCopyTextToClipboard(text, button);
    });
  } else {
    // Fallback for older browsers
    fallbackCopyTextToClipboard(text, button);
  }
}

// Fallback copy method
function fallbackCopyTextToClipboard(text, button) {
  const textArea = document.createElement('textarea');
  textArea.value = text;
  textArea.style.position = 'fixed';
  textArea.style.left = '-999999px';
  textArea.style.top = '-999999px';
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();
  
  try {
    document.execCommand('copy');
    showCopyFeedback(button);
  } catch (err) {
    console.error('Fallback: Oops, unable to copy', err);
  }
  
  document.body.removeChild(textArea);
}

// Show copy success feedback
function showCopyFeedback(button) {
  const originalText = button.textContent;
  button.textContent = 'Copied!';
  button.style.background = 'var(--neon-green)';
  button.style.color = 'var(--bg-dark)';
  button.style.boxShadow = 'var(--glow-green)';
  
  // Announce to screen readers
  announceToScreenReader('Code copied to clipboard');
  
  setTimeout(() => {
    button.textContent = originalText;
    button.style.background = 'transparent';
    button.style.color = 'var(--neon-green)';
    button.style.boxShadow = 'none';
  }, 2000);
}

// Initialize accessibility features
function initializeAccessibility() {
  // Add keyboard navigation
  document.addEventListener('keydown', function(e) {
    // ESC key closes open code panels
    if (e.key === 'Escape') {
      const openPanels = document.querySelectorAll('.code-panel:not([hidden])');
      openPanels.forEach(panel => {
        const button = document.querySelector(`[data-target="${panel.id}"]`);
        if (button) {
          const buttonText = button.querySelector('.btn-text');
          toggleCodePanel(panel, buttonText);
        }
      });
    }
  });
  
  // Add focus management
  const focusableElements = document.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  );
  
  focusableElements.forEach(element => {
    element.addEventListener('focus', function() {
      this.style.outline = '2px solid var(--neon-cyan)';
      this.style.outlineOffset = '2px';
    });
    
    element.addEventListener('blur', function() {
      this.style.outline = '';
      this.style.outlineOffset = '';
    });
  });
}

// Performance optimizations
function initializePerformanceOptimizations() {
  // Intersection Observer for lazy loading animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animate-in');
        observer.unobserve(entry.target);
      }
    });
  }, observerOptions);
  
  // Observe all cards
  const cards = document.querySelectorAll('.magic-card');
  cards.forEach(card => {
    observer.observe(card);
  });
  
  // Reduce motion for users who prefer it
  if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    document.documentElement.style.setProperty('--animation-duration', '0.01ms');
  }
}

// Screen reader announcements
function announceToScreenReader(message) {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', 'polite');
  announcement.setAttribute('aria-atomic', 'true');
  announcement.className = 'sr-only';
  announcement.textContent = message;
  
  document.body.appendChild(announcement);
  
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
}

// Utility function to debounce events
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Export functions for use in other modules
window.MagicTricks = {
  toggleCodePanel,
  copyToClipboard,
  announceToScreenReader,
  debounce
};
