/* ===================================
   GLOBAL ERROR HANDLER
   ================================= */

class ErrorHandler {
  constructor() {
    this.errors = [];
    this.init();
  }

  init() {
    this.setupGlobalErrorHandling();
    this.setupResourceErrorHandling();
    this.setupUnhandledRejectionHandling();
    console.log('🛡️ Error Handler initialized');
  }

  setupGlobalErrorHandling() {
    window.addEventListener('error', (event) => {
      this.handleError({
        type: 'JavaScript Error',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error,
        timestamp: new Date().toISOString(),
      });
    });
  }

  setupResourceErrorHandling() {
    window.addEventListener(
      'error',
      (event) => {
        if (event.target !== window) {
          this.handleResourceError({
            type: 'Resource Error',
            element: event.target.tagName,
            source: event.target.src || event.target.href,
            timestamp: new Date().toISOString(),
          });
        }
      },
      true
    );
  }

  setupUnhandledRejectionHandling() {
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError({
        type: 'Unhandled Promise Rejection',
        message: event.reason?.message || event.reason,
        timestamp: new Date().toISOString(),
      });
    });
  }

  handleError(errorInfo) {
    this.errors.push(errorInfo);

    // Log to console in development
    if (this.isDevelopment()) {
      console.group(`🚨 ${errorInfo.type}`);
      console.error('Error details:', errorInfo);
      console.groupEnd();
    }

    // Handle specific error types
    this.handleSpecificErrors(errorInfo);
  }

  handleResourceError(errorInfo) {
    this.errors.push(errorInfo);

    // Handle specific resource errors
    if (errorInfo.source) {
      if (errorInfo.source.includes('fonts.googleapis.com')) {
        console.warn('⚠️ Google Fonts failed to load, using fallback fonts');
        this.applyFallbackFonts();
      } else if (errorInfo.source.includes('cdnjs.cloudflare.com')) {
        console.warn('⚠️ CDN resource failed to load:', errorInfo.source);
        this.handleCDNFailure(errorInfo.source);
      }
    }
  }

  handleSpecificErrors(errorInfo) {
    // Handle duplicate identifier errors
    if (
      errorInfo.message &&
      errorInfo.message.includes('already been declared')
    ) {
      console.warn(
        '⚠️ Duplicate identifier detected, this is expected in some cases'
      );
      return; // Don't treat as critical error
    }

    // Handle module loading errors
    if (errorInfo.message && errorInfo.message.includes('module')) {
      console.warn('⚠️ Module loading issue:', errorInfo.message);
      return;
    }

    // Handle TypeScript-like errors that are not critical
    if (
      errorInfo.message &&
      ((errorInfo.message.includes('Property') &&
        errorInfo.message.includes('may not exist')) ||
        errorInfo.message.includes('is deprecated') ||
        errorInfo.message.includes('never read'))
    ) {
      console.warn('⚠️ Non-critical type warning:', errorInfo.message);
      return;
    }

    // Handle CDN resource failures gracefully
    if (errorInfo.message && errorInfo.message.includes('404')) {
      console.warn('⚠️ Resource not found (404), using fallbacks');
      return;
    }
  }

  applyFallbackFonts() {
    const style = document.createElement('style');
    style.textContent = `
      body, * {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
                     'Helvetica Neue', Arial, sans-serif !important;
      }
    `;
    document.head.appendChild(style);
  }

  handleCDNFailure(source) {
    // Handle specific CDN failures
    if (source.includes('prism')) {
      console.warn('⚠️ Prism.js failed to load, syntax highlighting disabled');
      this.disableSyntaxHighlighting();
    } else if (source.includes('clipboard')) {
      console.warn('⚠️ ClipboardJS failed to load, using fallback copy method');
      this.enableFallbackClipboard();
    }
  }

  disableSyntaxHighlighting() {
    // Add basic styling for code blocks without Prism
    const style = document.createElement('style');
    style.textContent = `
      pre code {
        background: rgba(0, 0, 0, 0.8);
        color: #fff;
        padding: 1rem;
        border-radius: 8px;
        display: block;
        overflow-x: auto;
        font-family: 'Courier New', monospace;
      }
    `;
    document.head.appendChild(style);
  }

  enableFallbackClipboard() {
    // Ensure clipboard functionality works without ClipboardJS
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('btn-copy')) {
        const targetId = e.target.getAttribute('data-clipboard-target');
        if (targetId) {
          const element = document.getElementById(targetId);
          if (element) {
            this.fallbackCopyText(element.textContent);
          }
        }
      }
    });
  }

  fallbackCopyText(text) {
    if (navigator.clipboard) {
      navigator.clipboard
        .writeText(text)
        .then(() => {
          console.log('✅ Text copied using Clipboard API');
        })
        .catch(() => {
          this.legacyCopyText(text);
        });
    } else {
      this.legacyCopyText(text);
    }
  }

  legacyCopyText(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      document.execCommand('copy');
      console.log('✅ Text copied using legacy method');
    } catch (err) {
      console.error('❌ Failed to copy text:', err);
    }

    document.body.removeChild(textArea);
  }

  isDevelopment() {
    return (
      window.location.hostname === 'localhost' ||
      window.location.hostname === '127.0.0.1' ||
      window.location.hostname.includes('local')
    );
  }

  getErrorStats() {
    const stats = {
      total: this.errors.length,
      byType: {},
      recent: this.errors.slice(-5),
    };

    this.errors.forEach((error) => {
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
    });

    return stats;
  }

  clearErrors() {
    this.errors = [];
    console.log('🧹 Error log cleared');
  }
}

// Initialize error handler immediately
window.errorHandler = new ErrorHandler();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ErrorHandler;
}
