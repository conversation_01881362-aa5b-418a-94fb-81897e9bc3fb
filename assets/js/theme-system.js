/* ===================================
   DYNAMIC THEMES SYSTEM
   ================================= */

class ThemeSystem {
  constructor() {
    this.themeButtons = document.querySelectorAll('.theme-btn');
    this.themePreview = document.querySelector('.theme-preview');
    this.currentTheme = 'dark';

    if (!this.themePreview) {
      console.warn('Theme System: Required elements not found');
      return;
    }

    this.init();
  }

  init() {
    this.setupThemeButtons();
    this.setTheme('dark');
  }

  setupThemeButtons() {
    this.themeButtons.forEach((button) => {
      button.addEventListener('click', () => {
        const theme = button.dataset.theme;
        this.setTheme(theme);
        this.updateActiveButton(button);
      });
    });
  }

  setTheme(theme) {
    this.currentTheme = theme;
    this.themePreview.setAttribute('data-demo-theme', theme);

    // Update CSS variables display
    this.updateVariablesDisplay();
  }

  updateActiveButton(activeButton) {
    this.themeButtons.forEach((button) => {
      button.classList.remove('active');
    });
    activeButton.classList.add('active');
  }

  updateVariablesDisplay() {
    const variableItems = document.querySelectorAll('.variable-value');
    const computedStyle = getComputedStyle(this.themePreview);

    variableItems.forEach((item) => {
      const variableName = item.previousElementSibling.textContent;
      const value = computedStyle.getPropertyValue(
        variableName.replace('--', '--theme-')
      );
      if (value) {
        item.textContent = value.trim();
      }
    });
  }

  getCurrentTheme() {
    return this.currentTheme;
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    if (!window.themeSystem) {
      window.themeSystem = new ThemeSystem();
    }
  }, 100);
});

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ThemeSystem;
}
