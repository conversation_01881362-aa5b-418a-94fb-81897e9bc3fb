/* ===================================
   COPY TO CLIPBOARD FUNCTIONALITY
   ================================= */

class ClipboardManager {
  constructor() {
    this.init();
  }

  init() {
    this.bindEvents();
    this.setupClipboardJS();
  }

  bindEvents() {
    // Handle copy button clicks
    document.addEventListener('click', (e) => {
      if (e.target.closest('.btn-copy')) {
        e.preventDefault();
        this.handleCopyClick(e.target.closest('.btn-copy'));
      }
    });

    // Keyboard support for copy buttons
    document.addEventListener('keydown', (e) => {
      if ((e.key === 'Enter' || e.key === ' ') && e.target.closest('.btn-copy')) {
        e.preventDefault();
        this.handleCopyClick(e.target.closest('.btn-copy'));
      }
    });
  }

  handleCopyClick(button) {
    const targetId = button.getAttribute('data-clipboard-target');
    const codeElement = document.getElementById(targetId);

    if (!codeElement) {
      console.error('Copy target not found:', targetId);
      return;
    }

    const textToCopy = this.extractTextContent(codeElement);
    this.copyToClipboard(textToCopy, button);
  }

  extractTextContent(element) {
    // Get clean text content without syntax highlighting markup
    const clone = element.cloneNode(true);
    
    // Remove any line numbers or other non-code elements
    const lineNumbers = clone.querySelectorAll('.line-numbers-rows, .line-numbers');
    lineNumbers.forEach(el => el.remove());
    
    // Get text content and clean it up
    let text = clone.textContent || clone.innerText || '';
    
    // Remove extra whitespace and normalize line endings
    text = text.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
    text = text.replace(/^\s+|\s+$/g, ''); // Trim start and end
    
    return text;
  }

  async copyToClipboard(text, button) {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        // Use modern Clipboard API
        await navigator.clipboard.writeText(text);
        this.showSuccessFeedback(button);
      } else {
        // Fallback for older browsers or non-secure contexts
        this.fallbackCopyTextToClipboard(text, button);
      }
    } catch (err) {
      console.error('Failed to copy text:', err);
      this.showErrorFeedback(button);
    }
  }

  fallbackCopyTextToClipboard(text, button) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    
    // Make the textarea invisible but accessible
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    textArea.style.opacity = '0';
    textArea.setAttribute('readonly', '');
    textArea.setAttribute('aria-hidden', 'true');
    
    document.body.appendChild(textArea);
    
    try {
      // Select and copy the text
      textArea.focus();
      textArea.select();
      textArea.setSelectionRange(0, 99999); // For mobile devices
      
      const successful = document.execCommand('copy');
      
      if (successful) {
        this.showSuccessFeedback(button);
      } else {
        this.showErrorFeedback(button);
      }
    } catch (err) {
      console.error('Fallback copy failed:', err);
      this.showErrorFeedback(button);
    } finally {
      document.body.removeChild(textArea);
    }
  }

  showSuccessFeedback(button) {
    const originalText = button.textContent;
    const originalStyles = {
      background: button.style.background,
      color: button.style.color,
      boxShadow: button.style.boxShadow,
      border: button.style.border
    };

    // Update button appearance
    button.textContent = 'Copied!';
    button.style.background = 'var(--neon-green)';
    button.style.color = 'var(--bg-dark)';
    button.style.boxShadow = 'var(--glow-green)';
    button.style.border = '1px solid var(--neon-green)';
    button.classList.add('copy-success');

    // Add success animation
    button.style.transform = 'scale(1.05)';
    
    // Announce to screen readers
    this.announceToScreenReader('Code copied to clipboard successfully');

    // Reset after delay
    setTimeout(() => {
      button.textContent = originalText;
      button.style.background = originalStyles.background;
      button.style.color = originalStyles.color;
      button.style.boxShadow = originalStyles.boxShadow;
      button.style.border = originalStyles.border;
      button.style.transform = '';
      button.classList.remove('copy-success');
    }, 2000);
  }

  showErrorFeedback(button) {
    const originalText = button.textContent;
    const originalStyles = {
      background: button.style.background,
      color: button.style.color,
      boxShadow: button.style.boxShadow,
      border: button.style.border
    };

    // Update button appearance for error
    button.textContent = 'Error!';
    button.style.background = 'var(--neon-pink)';
    button.style.color = 'var(--bg-dark)';
    button.style.boxShadow = 'var(--glow-pink)';
    button.style.border = '1px solid var(--neon-pink)';
    button.classList.add('copy-error');

    // Add error animation
    button.style.transform = 'scale(1.05)';
    
    // Announce to screen readers
    this.announceToScreenReader('Failed to copy code to clipboard');

    // Reset after delay
    setTimeout(() => {
      button.textContent = originalText;
      button.style.background = originalStyles.background;
      button.style.color = originalStyles.color;
      button.style.boxShadow = originalStyles.boxShadow;
      button.style.border = originalStyles.border;
      button.style.transform = '';
      button.classList.remove('copy-error');
    }, 2000);
  }

  setupClipboardJS() {
    // Initialize ClipboardJS if available
    if (typeof ClipboardJS !== 'undefined') {
      const clipboard = new ClipboardJS('.btn-copy', {
        text: (trigger) => {
          const targetId = trigger.getAttribute('data-clipboard-target');
          const codeElement = document.getElementById(targetId);
          return codeElement ? this.extractTextContent(codeElement) : '';
        }
      });

      clipboard.on('success', (e) => {
        this.showSuccessFeedback(e.trigger);
        e.clearSelection();
      });

      clipboard.on('error', (e) => {
        this.showErrorFeedback(e.trigger);
      });
    }
  }

  announceToScreenReader(message) {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'assertive');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    setTimeout(() => {
      if (document.body.contains(announcement)) {
        document.body.removeChild(announcement);
      }
    }, 1000);
  }

  // Public methods
  copyText(text) {
    return this.copyToClipboard(text, null);
  }

  copyElementContent(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
      const text = this.extractTextContent(element);
      return this.copyToClipboard(text, null);
    }
    return Promise.reject(new Error('Element not found'));
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  window.clipboardManager = new ClipboardManager();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ClipboardManager;
}
