/* ===================================
   CONTAINER QUERIES INTERACTIVE DEMO
   ================================= */

class ContainerQueriesDemo {
  constructor() {
    this.container = null;
    this.sizeIndicator = null;
    this.breakpointIndicators = null;
    this.resizeObserver = null;
    this.init();
  }

  init() {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setup());
    } else {
      this.setup();
    }
  }

  setup() {
    this.container = document.getElementById('container-demo');
    this.sizeIndicator = document.getElementById('size-display');
    this.breakpointIndicators = document.querySelectorAll('.breakpoint');

    if (!this.container || !this.sizeIndicator) {
      console.warn('Container queries demo elements not found');
      return;
    }

    this.setupResizeObserver();
    this.setupBreakpointClicks();
    this.updateDisplay();

    console.log('✅ Container Queries demo initialized');
  }

  setupResizeObserver() {
    // Use ResizeObserver to watch for container size changes
    this.resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        this.updateDisplay();
      }
    });

    this.resizeObserver.observe(this.container);

    // Also listen for manual resize events
    this.container.addEventListener('mouseup', () => {
      setTimeout(() => this.updateDisplay(), 100);
    });

    // Listen for window resize to update on mobile
    window.addEventListener('resize', () => {
      setTimeout(() => this.updateDisplay(), 100);
    });
  }

  setupBreakpointClicks() {
    this.breakpointIndicators.forEach((indicator) => {
      indicator.addEventListener('click', () => {
        const size = indicator.dataset.size;
        this.jumpToBreakpoint(size);
      });
    });
  }

  jumpToBreakpoint(sizeRange) {
    let targetWidth;

    switch (sizeRange) {
      case '< 350px':
        targetWidth = 300;
        break;
      case '350-549px':
        targetWidth = 450;
        break;
      case '550px+':
        targetWidth = 650;
        break;
      default:
        return;
    }

    // Animate to target width
    this.animateToWidth(targetWidth);
  }

  animateToWidth(targetWidth) {
    const currentWidth = this.container.offsetWidth;
    const duration = 500;
    const startTime = performance.now();

    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Easing function for smooth animation
      const easeInOutCubic =
        progress < 0.5
          ? 4 * progress * progress * progress
          : 1 - Math.pow(-2 * progress + 2, 3) / 2;

      const currentAnimatedWidth =
        currentWidth + (targetWidth - currentWidth) * easeInOutCubic;

      this.container.style.width = `${currentAnimatedWidth}px`;
      this.updateDisplay();

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);
  }

  updateDisplay() {
    const width = this.container.offsetWidth;

    // Update size indicator
    this.sizeIndicator.textContent = `${width}px`;

    // Update breakpoint indicators
    this.updateBreakpointIndicators(width);

    // Update size indicator color based on breakpoint
    this.updateSizeIndicatorColor(width);
  }

  updateBreakpointIndicators(width) {
    // Remove all active classes
    this.breakpointIndicators.forEach((indicator) => {
      indicator.classList.remove('active');
    });

    // Add active class to current breakpoint
    if (width < 350) {
      document.querySelector('.breakpoint.small')?.classList.add('active');
    } else if (width >= 350 && width < 550) {
      document.querySelector('.breakpoint.medium')?.classList.add('active');
    } else {
      document.querySelector('.breakpoint.large')?.classList.add('active');
    }
  }

  updateSizeIndicatorColor(width) {
    // Change indicator color based on breakpoint
    if (width < 350) {
      this.sizeIndicator.style.background = 'var(--neon-pink)';
      this.sizeIndicator.style.boxShadow = 'var(--glow-pink-soft)';
    } else if (width >= 350 && width < 550) {
      this.sizeIndicator.style.background = 'var(--neon-cyan)';
      this.sizeIndicator.style.boxShadow = 'var(--glow-cyan-soft)';
    } else {
      this.sizeIndicator.style.background = 'var(--neon-purple)';
      this.sizeIndicator.style.boxShadow = 'var(--glow-purple-soft)';
    }
  }

  // Public methods for external control
  getCurrentWidth() {
    return this.container ? this.container.offsetWidth : 0;
  }

  setWidth(width) {
    if (this.container) {
      this.container.style.width = `${width}px`;
      this.updateDisplay();
    }
  }

  getBreakpointInfo() {
    const width = this.getCurrentWidth();

    if (width < 350) {
      return { name: 'small', range: '< 350px', width };
    } else if (width >= 350 && width < 550) {
      return { name: 'medium', range: '350-549px', width };
    } else {
      return { name: 'large', range: '550px+', width };
    }
  }

  destroy() {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  }
}

// Initialize the demo
let containerQueriesDemo;

document.addEventListener('DOMContentLoaded', () => {
  containerQueriesDemo = new ContainerQueriesDemo();
});

// Export for global access
window.ContainerQueriesDemo = ContainerQueriesDemo;
window.containerQueriesDemo = containerQueriesDemo;

// Add to MagicTricks namespace if available
if (typeof window.MagicTricks !== 'undefined') {
  window.MagicTricks.containerQueriesDemo = containerQueriesDemo;
}
