/* ===================================
   SERVICE WORKER REGISTRATION
   ================================= */

class ServiceWorkerManager {
  constructor() {
    this.swRegistration = null;
    this.isOnline = navigator.onLine;
    this.init();
  }

  async init() {
    if ('serviceWorker' in navigator) {
      try {
        await this.registerServiceWorker();
        this.setupOnlineOfflineHandlers();
        this.setupUpdateHandlers();
        console.log('🔧 Service Worker Manager initialized');
      } catch (error) {
        console.error('❌ Service Worker registration failed:', error);
      }
    } else {
      console.warn('⚠️ Service Workers not supported in this browser');
    }
  }

  async registerServiceWorker() {
    try {
      this.swRegistration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
      });

      console.log('✅ Service Worker registered successfully');

      // Check for updates
      this.swRegistration.addEventListener('updatefound', () => {
        this.handleUpdateFound();
      });

      // Handle controller change
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        this.handleControllerChange();
      });
    } catch (error) {
      console.error('❌ Service Worker registration failed:', error);
      throw error;
    }
  }

  handleUpdateFound() {
    const newWorker = this.swRegistration.installing;

    if (newWorker) {
      console.log('🔄 New Service Worker found, installing...');

      newWorker.addEventListener('statechange', () => {
        if (newWorker.state === 'installed') {
          if (navigator.serviceWorker.controller) {
            // New update available
            this.showUpdateNotification();
          } else {
            // First time installation
            this.showInstallNotification();
          }
        }
      });
    }
  }

  handleControllerChange() {
    console.log('🔄 Service Worker controller changed, reloading...');
    window.location.reload();
  }

  setupOnlineOfflineHandlers() {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.handleOnline();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.handleOffline();
    });

    // Initial state
    if (this.isOnline) {
      this.handleOnline();
    } else {
      this.handleOffline();
    }
  }

  handleOnline() {
    console.log('🌐 Back online');
    this.hideOfflineNotification();
    this.syncWhenOnline();
  }

  handleOffline() {
    console.log('🔌 Gone offline');
    this.showOfflineNotification();
  }

  showOfflineNotification() {
    const notification = this.createNotification(
      "🔌 You're offline",
      'Some features may be limited. Content will be served from cache.',
      'offline-notification',
      'info'
    );

    document.body.appendChild(notification);
  }

  hideOfflineNotification() {
    const notification = document.getElementById('offline-notification');
    if (notification) {
      notification.remove();
    }
  }

  showUpdateNotification() {
    const notification = this.createNotification(
      '🔄 Update Available',
      'A new version is available. Refresh to update.',
      'update-notification',
      'update',
      () => this.applyUpdate()
    );

    document.body.appendChild(notification);
  }

  showInstallNotification() {
    const notification = this.createNotification(
      '✅ App Installed',
      'CSS Magic Tricks is now available offline!',
      'install-notification',
      'success'
    );

    document.body.appendChild(notification);

    // Auto-hide after 5 seconds
    setTimeout(() => {
      notification.remove();
    }, 5000);
  }

  createNotification(title, message, id, type, action = null) {
    const notification = document.createElement('div');
    notification.id = id;
    notification.className = `sw-notification sw-notification--${type}`;

    notification.innerHTML = `
      <div class="sw-notification__content">
        <h4 class="sw-notification__title">${title}</h4>
        <p class="sw-notification__message">${message}</p>
        <div class="sw-notification__actions">
          ${
            action
              ? '<button class="sw-notification__action">Update</button>'
              : ''
          }
          <button class="sw-notification__close">×</button>
        </div>
      </div>
    `;

    // Add styles
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: var(--bg-card);
      border: 1px solid var(--neon-cyan);
      border-radius: 8px;
      padding: 1rem;
      max-width: 300px;
      z-index: 10000;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      animation: slideIn 0.3s ease;
    `;

    // Add event listeners
    const closeBtn = notification.querySelector('.sw-notification__close');
    closeBtn.addEventListener('click', () => notification.remove());

    if (action) {
      const actionBtn = notification.querySelector('.sw-notification__action');
      actionBtn.addEventListener('click', action);
    }

    return notification;
  }

  async applyUpdate() {
    if (this.swRegistration && this.swRegistration.waiting) {
      // Tell the waiting service worker to skip waiting
      this.swRegistration.waiting.postMessage({ type: 'SKIP_WAITING' });
    }
  }

  async syncWhenOnline() {
    if (
      'serviceWorker' in navigator &&
      'sync' in window.ServiceWorkerRegistration.prototype
    ) {
      try {
        await this.swRegistration.sync.register('analytics-sync');
        console.log('📊 Background sync registered');
      } catch (error) {
        console.error('❌ Background sync registration failed:', error);
      }
    }
  }

  setupUpdateHandlers() {
    // Listen for messages from service worker
    navigator.serviceWorker.addEventListener('message', (event) => {
      if (event.data && event.data.type === 'CACHE_UPDATED') {
        console.log('📦 Cache updated:', event.data.url);
      }
    });
  }

  // Public methods for manual control
  async checkForUpdates() {
    if (this.swRegistration) {
      await this.swRegistration.update();
    }
  }

  async unregister() {
    if (this.swRegistration) {
      await this.swRegistration.unregister();
      console.log('🗑️ Service Worker unregistered');
    }
  }

  getRegistration() {
    return this.swRegistration;
  }

  isSupported() {
    return 'serviceWorker' in navigator;
  }

  getNetworkStatus() {
    return {
      online: this.isOnline,
      connection: navigator.connection || null,
    };
  }
}

// Add CSS for notifications
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
  @keyframes slideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  .sw-notification {
    font-family: 'Nunito Sans', sans-serif;
    color: var(--text-primary);
  }

  .sw-notification__title {
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--neon-cyan);
  }

  .sw-notification__message {
    margin: 0 0 1rem 0;
    font-size: 0.8rem;
    line-height: 1.4;
    color: var(--text-secondary);
  }

  .sw-notification__actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
  }

  .sw-notification__action,
  .sw-notification__close {
    background: transparent;
    border: 1px solid var(--neon-cyan);
    color: var(--neon-cyan);
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .sw-notification__action:hover,
  .sw-notification__close:hover {
    background: var(--neon-cyan);
    color: var(--bg-dark);
  }

  .sw-notification--offline {
    border-color: var(--neon-orange);
  }

  .sw-notification--offline .sw-notification__title {
    color: var(--neon-orange);
  }

  .sw-notification--update {
    border-color: var(--neon-purple);
  }

  .sw-notification--update .sw-notification__title {
    color: var(--neon-purple);
  }

  .sw-notification--success {
    border-color: var(--neon-green);
  }

  .sw-notification--success .sw-notification__title {
    color: var(--neon-green);
  }
`;

document.head.appendChild(notificationStyles);

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  if (!window.swManager) {
    window.swManager = new ServiceWorkerManager();
  }
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ServiceWorkerManager;
}
