/* ===================================
   IMAGE OPTIMIZATION & LAZY LOADING
   ================================= */

class ImageOptimizer {
  constructor() {
    this.lazyImages = [];
    this.imageObserver = null;
    this.init();
  }

  init() {
    this.setupLazyLoading();
    this.setupResponsiveImages();
    this.setupImageErrorHandling();
    console.log('🖼️ Image Optimizer initialized');
  }

  setupLazyLoading() {
    // Check if Intersection Observer is supported
    if ('IntersectionObserver' in window) {
      this.imageObserver = new IntersectionObserver(
        (entries, observer) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const img = entry.target;
              this.loadImage(img);
              observer.unobserve(img);
            }
          });
        },
        {
          rootMargin: '50px 0px',
          threshold: 0.01
        }
      );

      // Observe all lazy images
      this.observeLazyImages();
    } else {
      // Fallback for browsers without Intersection Observer
      this.loadAllImages();
    }
  }

  observeLazyImages() {
    const lazyImages = document.querySelectorAll('img[loading="lazy"]');
    lazyImages.forEach(img => {
      this.imageObserver.observe(img);
    });
  }

  loadImage(img) {
    // Handle srcset for different densities
    if (img.dataset.srcset) {
      img.srcset = img.dataset.srcset;
    }
    
    if (img.dataset.src) {
      img.src = img.dataset.src;
    }

    img.onload = () => {
      img.classList.add('loaded');
      img.removeAttribute('loading');
    };

    img.onerror = () => {
      this.handleImageError(img);
    };
  }

  loadAllImages() {
    const lazyImages = document.querySelectorAll('img[loading="lazy"]');
    lazyImages.forEach(img => this.loadImage(img));
  }

  setupResponsiveImages() {
    // Create responsive image sources for different densities
    const images = document.querySelectorAll('img:not([srcset])');
    images.forEach(img => {
      if (img.src && !img.dataset.optimized) {
        this.createResponsiveSources(img);
      }
    });
  }

  createResponsiveSources(img) {
    const src = img.src;
    const extension = src.split('.').pop();
    const baseName = src.replace(`.${extension}`, '');

    // Create srcset for different densities (if images exist)
    const srcset = [
      `${src} 1x`,
      `${baseName}@2x.${extension} 2x`,
      `${baseName}@3x.${extension} 3x`
    ].join(', ');

    // Only set srcset if we have multiple sources
    if (this.checkImageExists(`${baseName}@2x.${extension}`)) {
      img.srcset = srcset;
    }

    img.dataset.optimized = 'true';
  }

  checkImageExists(url) {
    // Simple check - in production, you might want to precheck these
    return false; // For now, assume additional densities don't exist
  }

  setupImageErrorHandling() {
    document.addEventListener('error', (e) => {
      if (e.target.tagName === 'IMG') {
        this.handleImageError(e.target);
      }
    }, true);
  }

  handleImageError(img) {
    console.warn('Failed to load image:', img.src);
    
    // Add error class for styling
    img.classList.add('image-error');
    
    // Create placeholder
    this.createImagePlaceholder(img);
  }

  createImagePlaceholder(img) {
    const placeholder = document.createElement('div');
    placeholder.className = 'image-placeholder';
    placeholder.style.cssText = `
      width: ${img.width || 200}px;
      height: ${img.height || 150}px;
      background: linear-gradient(45deg, var(--bg-medium), var(--bg-light));
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-secondary);
      font-size: 0.8rem;
      text-align: center;
      max-width: 100%;
      box-sizing: border-box;
    `;
    placeholder.innerHTML = '🖼️<br>Image not available';
    
    img.parentNode.replaceChild(placeholder, img);
  }

  // Public method to add new images dynamically
  addLazyImage(img) {
    if (this.imageObserver) {
      this.imageObserver.observe(img);
    } else {
      this.loadImage(img);
    }
  }

  // Method to preload critical images
  preloadCriticalImages() {
    const criticalImages = document.querySelectorAll('img[data-critical="true"]');
    criticalImages.forEach(img => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = img.src || img.dataset.src;
      document.head.appendChild(link);
    });
  }

  // Method to optimize existing images
  optimizeExistingImages() {
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      // Add loading="lazy" to non-critical images
      if (!img.hasAttribute('loading') && !img.dataset.critical) {
        img.loading = 'lazy';
      }

      // Add responsive classes based on context
      if (img.closest('.card-avatar, .avatar')) {
        img.classList.add('avatar-image');
      } else if (img.closest('.demo, .card-demo')) {
        img.classList.add('demo-image');
      } else if (img.closest('.icon, .btn-icon')) {
        img.classList.add('icon-image');
      }
    });
  }

  // Performance monitoring
  getImageStats() {
    const allImages = document.querySelectorAll('img');
    const loadedImages = document.querySelectorAll('img.loaded');
    const errorImages = document.querySelectorAll('img.image-error');
    
    return {
      total: allImages.length,
      loaded: loadedImages.length,
      errors: errorImages.length,
      pending: allImages.length - loadedImages.length - errorImages.length
    };
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  window.imageOptimizer = new ImageOptimizer();
  
  // Preload critical images
  setTimeout(() => {
    window.imageOptimizer.preloadCriticalImages();
    window.imageOptimizer.optimizeExistingImages();
  }, 100);
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ImageOptimizer;
}
