/* ===================================
   HOUDINI DEMO SIMULATOR
   ================================= */

class HoudiniDemo {
  constructor() {
    this.canvas = document.getElementById('houdini-canvas');
    this.rotationSlider = document.getElementById('rotation-slider');
    this.opacitySlider = document.getElementById('opacity-slider');
    this.color1Picker = document.getElementById('color1-picker');
    this.color2Picker = document.getElementById('color2-picker');
    this.rotationValue = document.getElementById('rotation-value');
    this.opacityValue = document.getElementById('opacity-value');
    
    if (!this.canvas) {
      console.warn('Houdini Demo: Required elements not found');
      return;
    }
    
    this.init();
  }
  
  init() {
    this.setupControls();
    this.updateCanvas();
  }
  
  setupControls() {
    if (this.rotationSlider) {
      this.rotationSlider.addEventListener('input', () => {
        this.updateRotation();
      });
    }
    
    if (this.opacitySlider) {
      this.opacitySlider.addEventListener('input', () => {
        this.updateOpacity();
      });
    }
    
    if (this.color1Picker) {
      this.color1Picker.addEventListener('input', () => {
        this.updateColors();
      });
    }
    
    if (this.color2Picker) {
      this.color2Picker.addEventListener('input', () => {
        this.updateColors();
      });
    }
    
    // Mouse tracking for interactive effect
    if (this.canvas) {
      this.canvas.addEventListener('mousemove', (e) => {
        this.updateMousePosition(e);
      });
    }
  }
  
  updateRotation() {
    const rotation = this.rotationSlider.value;
    this.canvas.style.setProperty('--rotation', `${rotation}deg`);
    if (this.rotationValue) {
      this.rotationValue.textContent = `${rotation}°`;
    }
  }
  
  updateOpacity() {
    const opacity = this.opacitySlider.value / 100;
    this.canvas.style.setProperty('--opacity', opacity);
    if (this.opacityValue) {
      this.opacityValue.textContent = `${this.opacitySlider.value}%`;
    }
  }
  
  updateColors() {
    const color1 = this.color1Picker.value;
    const color2 = this.color2Picker.value;
    this.canvas.style.setProperty('--color-1', color1);
    this.canvas.style.setProperty('--color-2', color2);
  }
  
  updateMousePosition(e) {
    const rect = this.canvas.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;
    
    this.canvas.style.setProperty('--mouse-x', `${x}%`);
    this.canvas.style.setProperty('--mouse-y', `${y}%`);
  }
  
  updateCanvas() {
    // Set initial values
    this.updateRotation();
    this.updateOpacity();
    this.updateColors();
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    window.houdiniDemo = new HoudiniDemo();
  }, 100);
});

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = HoudiniDemo;
}
