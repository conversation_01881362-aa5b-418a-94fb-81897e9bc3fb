/* ===================================
   CODE TOGGLE FUNCTIONALITY
   ================================= */

class CodeToggle {
  constructor() {
    this.activePanel = null;
    this.init();
  }

  init() {
    this.bindEvents();
    this.setupKeyboardNavigation();
  }

  bindEvents() {
    // Handle toggle button clicks
    document.addEventListener('click', (e) => {
      if (e.target.closest('.btn-show-code')) {
        e.preventDefault();
        this.handleToggleClick(e.target.closest('.btn-show-code'));
      }
    });
  }

  handleToggleClick(button) {
    const targetId = button.getAttribute('data-target');
    const panel = document.getElementById(targetId);
    const buttonText = button.querySelector('.btn-text');
    const buttonIcon = button.querySelector('.btn-icon');

    if (!panel) return;

    const isHidden = panel.hasAttribute('hidden');

    if (isHidden) {
      this.showPanel(panel, button, buttonText, buttonIcon);
    } else {
      this.hidePanel(panel, button, buttonText, buttonIcon);
    }
  }

  showPanel(panel, button, buttonText, buttonIcon) {
    // Hide any currently active panel
    if (this.activePanel && this.activePanel !== panel) {
      this.hideActivePanel();
    }

    // Show the panel
    panel.removeAttribute('hidden');
    panel.style.display = 'block';
    panel.style.animation = 'slideDown 0.3s ease-out forwards';
    
    // Update button state
    buttonText.textContent = 'Hide Code';
    if (buttonIcon) buttonIcon.textContent = '⚡';
    button.setAttribute('aria-expanded', 'true');
    button.classList.add('active');

    // Set as active panel
    this.activePanel = panel;

    // Focus management
    this.manageFocus(panel, true);

    // Announce to screen readers
    this.announceChange('Code panel opened', panel);

    // Scroll panel into view if needed
    setTimeout(() => {
      this.scrollIntoViewIfNeeded(panel);
    }, 300);
  }

  hidePanel(panel, button, buttonText, buttonIcon) {
    // Hide the panel
    panel.style.animation = 'slideUp 0.3s ease-out forwards';
    
    // Update button state
    buttonText.textContent = 'Show Code';
    if (buttonIcon) buttonIcon.textContent = '⚡';
    button.setAttribute('aria-expanded', 'false');
    button.classList.remove('active');

    // Clear active panel
    if (this.activePanel === panel) {
      this.activePanel = null;
    }

    // Focus management
    this.manageFocus(panel, false);

    // Announce to screen readers
    this.announceChange('Code panel closed', panel);

    // Hide after animation
    setTimeout(() => {
      panel.setAttribute('hidden', '');
      panel.style.display = 'none';
    }, 300);
  }

  hideActivePanel() {
    if (!this.activePanel) return;

    const button = document.querySelector(`[data-target="${this.activePanel.id}"]`);
    if (button) {
      const buttonText = button.querySelector('.btn-text');
      const buttonIcon = button.querySelector('.btn-icon');
      this.hidePanel(this.activePanel, button, buttonText, buttonIcon);
    }
  }

  setupKeyboardNavigation() {
    document.addEventListener('keydown', (e) => {
      // ESC key closes active panel
      if (e.key === 'Escape' && this.activePanel) {
        this.hideActivePanel();
        
        // Return focus to the toggle button
        const button = document.querySelector(`[data-target="${this.activePanel.id}"]`);
        if (button) {
          button.focus();
        }
      }

      // Enter/Space on toggle buttons
      if ((e.key === 'Enter' || e.key === ' ') && e.target.closest('.btn-show-code')) {
        e.preventDefault();
        this.handleToggleClick(e.target.closest('.btn-show-code'));
      }
    });
  }

  manageFocus(panel, isOpening) {
    if (isOpening) {
      // Store the currently focused element
      this.previousFocus = document.activeElement;
      
      // Focus the first focusable element in the panel
      setTimeout(() => {
        const firstFocusable = panel.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
        if (firstFocusable) {
          firstFocusable.focus();
        }
      }, 100);
    } else {
      // Return focus to the previously focused element
      if (this.previousFocus) {
        this.previousFocus.focus();
        this.previousFocus = null;
      }
    }
  }

  scrollIntoViewIfNeeded(panel) {
    const rect = panel.getBoundingClientRect();
    const isVisible = (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );

    if (!isVisible) {
      panel.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest'
      });
    }
  }

  announceChange(message, panel) {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    setTimeout(() => {
      if (document.body.contains(announcement)) {
        document.body.removeChild(announcement);
      }
    }, 1000);
  }

  // Public methods
  closeAllPanels() {
    const openPanels = document.querySelectorAll('.code-panel:not([hidden])');
    openPanels.forEach(panel => {
      const button = document.querySelector(`[data-target="${panel.id}"]`);
      if (button) {
        const buttonText = button.querySelector('.btn-text');
        const buttonIcon = button.querySelector('.btn-icon');
        this.hidePanel(panel, button, buttonText, buttonIcon);
      }
    });
  }

  openPanel(panelId) {
    const panel = document.getElementById(panelId);
    const button = document.querySelector(`[data-target="${panelId}"]`);
    
    if (panel && button) {
      const buttonText = button.querySelector('.btn-text');
      const buttonIcon = button.querySelector('.btn-icon');
      this.showPanel(panel, button, buttonText, buttonIcon);
    }
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  window.codeToggle = new CodeToggle();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CodeToggle;
}
