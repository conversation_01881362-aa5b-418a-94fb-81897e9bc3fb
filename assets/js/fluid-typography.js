/* ===================================
   FLUID TYPOGRAPHY SIMULATOR
   ================================= */

class FluidTypographySimulator {
  constructor() {
    this.viewport = document.getElementById('simulated-viewport');
    this.slider = document.getElementById('viewport-slider');
    this.display = document.getElementById('viewport-display');
    this.presetButtons = document.querySelectorAll('.preset-btn');
    
    if (!this.viewport || !this.slider || !this.display) {
      console.warn('Fluid Typography Simulator: Required elements not found');
      return;
    }
    
    this.init();
  }
  
  init() {
    this.setupSlider();
    this.setupPresets();
    this.updateViewport();
    
    // Set initial state
    this.setViewportWidth(768);
  }
  
  setupSlider() {
    this.slider.addEventListener('input', () => {
      this.updateViewport();
    });
    
    this.slider.addEventListener('change', () => {
      this.updateViewport();
    });
  }
  
  setupPresets() {
    this.presetButtons.forEach(button => {
      button.addEventListener('click', () => {
        const width = parseInt(button.dataset.width);
        this.setViewportWidth(width);
        this.updateActivePreset(button);
      });
    });
  }
  
  updateViewport() {
    const width = parseInt(this.slider.value);
    this.display.textContent = `${width}px`;
    
    // Update viewport width
    this.viewport.style.width = `${Math.min(width, 800)}px`;
    
    // Update active preset
    this.updateActivePresetByWidth(width);
    
    // Update CSS custom property for clamp calculations
    document.documentElement.style.setProperty('--simulated-vw', `${width}px`);
    
    // Trigger reflow to update clamp() calculations
    this.viewport.style.fontSize = `${Math.max(12, width * 0.02)}px`;
  }
  
  setViewportWidth(width) {
    this.slider.value = width;
    this.updateViewport();
  }
  
  updateActivePreset(activeButton) {
    this.presetButtons.forEach(button => {
      button.classList.remove('active');
    });
    activeButton.classList.add('active');
  }
  
  updateActivePresetByWidth(width) {
    this.presetButtons.forEach(button => {
      button.classList.remove('active');
      const buttonWidth = parseInt(button.dataset.width);
      if (Math.abs(buttonWidth - width) < 20) {
        button.classList.add('active');
      }
    });
  }
  
  // Public method to animate to specific width
  animateToWidth(targetWidth) {
    const currentWidth = parseInt(this.slider.value);
    const duration = 1000;
    const startTime = performance.now();
    
    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // Easing function
      const easeInOutCubic = progress < 0.5 
        ? 4 * progress * progress * progress 
        : 1 - Math.pow(-2 * progress + 2, 3) / 2;
      
      const currentValue = currentWidth + (targetWidth - currentWidth) * easeInOutCubic;
      this.setViewportWidth(Math.round(currentValue));
      
      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };
    
    requestAnimationFrame(animate);
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Wait a bit to ensure all elements are rendered
  setTimeout(() => {
    window.fluidTypographySimulator = new FluidTypographySimulator();
  }, 100);
});

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = FluidTypographySimulator;
}
