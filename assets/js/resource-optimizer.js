/* ===================================
   RESOURCE LOADING OPTIMIZER
   ================================= */

class ResourceOptimizer {
  constructor() {
    this.loadedResources = new Set();
    this.pendingResources = new Map();
    this.criticalResources = new Set();
    this.init();
  }

  init() {
    this.setupCriticalResourceLoading();
    this.setupLazyLoading();
    this.setupPrefetching();
    this.setupResourceHints();
    this.monitorPerformance();
    console.log('⚡ Resource Optimizer initialized');
  }

  setupCriticalResourceLoading() {
    // Preload critical CSS
    this.preloadResource('/assets/css/main.css', 'style', 'high');
    this.preloadResource('/assets/css/neon-theme.css', 'style', 'high');

    // Preload critical JavaScript
    this.preloadResource('/assets/js/main.js', 'script', 'high');
    this.preloadResource('/assets/js/code-toggle.js', 'script', 'medium');

    // Preload critical fonts
    this.preloadResource(
      'https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@200;300;400;500;600;700;800;900&display=swap',
      'style',
      'high',
      true
    );
  }

  preloadResource(href, as, priority = 'medium', crossorigin = false) {
    if (this.loadedResources.has(href)) {
      return;
    }

    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = as;

    if (priority === 'high') {
      link.fetchPriority = 'high';
    }

    if (crossorigin) {
      link.crossOrigin = 'anonymous';
    }

    link.onload = () => {
      this.loadedResources.add(href);
      console.log(`✅ Preloaded: ${href}`);
    };

    link.onerror = () => {
      console.warn(`❌ Failed to preload: ${href}`);
    };

    document.head.appendChild(link);
    this.criticalResources.add(href);
  }

  setupLazyLoading() {
    // Lazy load non-critical CSS
    this.lazyLoadCSS([
      '/assets/css/tricks/container-queries.css',
      '/assets/css/tricks/tooltips.css',
      '/assets/css/tricks/hover-effects.css',
      '/assets/css/tricks/fluid-typography.css',
      '/assets/css/tricks/scroll-snap.css',
      '/assets/css/tricks/theme-system.css',
      '/assets/css/tricks/grid-layouts.css',
      '/assets/css/tricks/animations.css',
      '/assets/css/tricks/advanced-selectors.css',
      '/assets/css/tricks/houdini.css',
    ]);

    // Lazy load non-critical JavaScript
    this.lazyLoadJS([
      '/assets/js/container-queries.js',
      '/assets/js/scroll-snap.js',
      '/assets/js/theme-system.js',
      '/assets/js/houdini-demo.js',
    ]);
  }

  lazyLoadCSS(urls) {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            urls.forEach((url) => this.loadCSS(url));
            observer.disconnect();
          }
        });
      },
      { threshold: 0.1 }
    );

    // Start loading when first card is visible
    const firstCard = document.querySelector('.magic-card');
    if (firstCard) {
      observer.observe(firstCard);
    } else {
      // Fallback: load after a delay
      setTimeout(() => {
        urls.forEach((url) => this.loadCSS(url));
      }, 1000);
    }
  }

  lazyLoadJS(urls) {
    // Load JavaScript files when needed
    setTimeout(() => {
      urls.forEach((url) => this.loadJS(url));
    }, 2000);
  }

  loadCSS(href) {
    if (this.loadedResources.has(href)) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = href;

      link.onload = () => {
        this.loadedResources.add(href);
        console.log(`📄 Loaded CSS: ${href}`);
        resolve();
      };

      link.onerror = () => {
        console.warn(`❌ Failed to load CSS: ${href}`);
        reject(new Error(`Failed to load CSS: ${href}`));
      };

      document.head.appendChild(link);
    });
  }

  loadJS(src) {
    if (this.loadedResources.has(src)) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = src;
      script.async = true;

      script.onload = () => {
        this.loadedResources.add(src);
        console.log(`📜 Loaded JS: ${src}`);
        resolve();
      };

      script.onerror = () => {
        console.warn(`❌ Failed to load JS: ${src}`);
        reject(new Error(`Failed to load JS: ${src}`));
      };

      document.head.appendChild(script);
    });
  }

  setupPrefetching() {
    // Prefetch resources that might be needed soon
    const prefetchUrls = [
      // Only prefetch resources that actually exist
      'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-css.min.js',
      'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-javascript.min.js',
    ];

    // Prefetch after initial load
    window.addEventListener('load', () => {
      setTimeout(() => {
        prefetchUrls.forEach((url) => this.prefetchResource(url));
      }, 3000);
    });
  }

  prefetchResource(href) {
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = href;

    link.onload = () => {
      console.log(`🔮 Prefetched: ${href}`);
    };

    document.head.appendChild(link);
  }

  setupResourceHints() {
    // DNS prefetch for external domains
    this.addDNSPrefetch([
      '//fonts.googleapis.com',
      '//fonts.gstatic.com',
      '//cdnjs.cloudflare.com',
    ]);

    // Preconnect to critical external domains
    this.addPreconnect([
      'https://fonts.googleapis.com',
      'https://fonts.gstatic.com',
    ]);
  }

  addDNSPrefetch(domains) {
    domains.forEach((domain) => {
      const link = document.createElement('link');
      link.rel = 'dns-prefetch';
      link.href = domain;
      document.head.appendChild(link);
    });
  }

  addPreconnect(urls) {
    urls.forEach((url) => {
      const link = document.createElement('link');
      link.rel = 'preconnect';
      link.href = url;
      link.crossOrigin = 'anonymous';
      document.head.appendChild(link);
    });
  }

  monitorPerformance() {
    // Monitor resource loading performance
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'resource') {
            this.analyzeResourcePerformance(entry);
          }
        });
      });

      observer.observe({ entryTypes: ['resource'] });
    }

    // Monitor Core Web Vitals
    this.monitorCoreWebVitals();
  }

  analyzeResourcePerformance(entry) {
    const duration = entry.responseEnd - entry.startTime;

    if (duration > 1000) {
      console.warn(
        `🐌 Slow resource: ${entry.name} (${duration.toFixed(2)}ms)`
      );
    }

    // Track critical resource performance
    if (this.criticalResources.has(entry.name)) {
      console.log(
        `⚡ Critical resource loaded: ${entry.name} (${duration.toFixed(2)}ms)`
      );
    }
  }

  monitorCoreWebVitals() {
    // Largest Contentful Paint
    if ('PerformanceObserver' in window) {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        console.log('📊 LCP:', lastEntry.startTime.toFixed(2) + 'ms');
      });

      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

      // First Input Delay
      const fidObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          console.log(
            '📊 FID:',
            entry.processingStart - entry.startTime + 'ms'
          );
        });
      });

      fidObserver.observe({ entryTypes: ['first-input'] });

      // Cumulative Layout Shift
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0;
        list.getEntries().forEach((entry) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        console.log('📊 CLS:', clsValue.toFixed(4));
      });

      clsObserver.observe({ entryTypes: ['layout-shift'] });
    }
  }

  // Public methods
  getLoadedResources() {
    return Array.from(this.loadedResources);
  }

  getPerformanceStats() {
    if ('performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0];
      return {
        domContentLoaded:
          navigation.domContentLoadedEventEnd -
          navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        totalTime: navigation.loadEventEnd - navigation.fetchStart,
        resourceCount: performance.getEntriesByType('resource').length,
      };
    }
    return null;
  }

  // Force load all remaining resources
  loadAllResources() {
    console.log('🚀 Loading all remaining resources...');
    // Implementation would load any remaining lazy resources
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  if (!window.resourceOptimizer) {
    window.resourceOptimizer = new ResourceOptimizer();
  }
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ResourceOptimizer;
}
