/* ===================================
   SCROLL SNAP GALLERY
   ================================= */

class ScrollSnapGallery {
  constructor() {
    this.container = document.getElementById('snap-container');
    this.prevBtn = document.getElementById('prev-btn');
    this.nextBtn = document.getElementById('next-btn');
    this.indicators = document.querySelectorAll('.indicator-dot');
    this.items = document.querySelectorAll('.snap-item');
    
    if (!this.container || !this.prevBtn || !this.nextBtn) {
      console.warn('Scroll Snap Gallery: Required elements not found');
      return;
    }
    
    this.currentIndex = 0;
    this.itemWidth = 280 + 16; // item width + gap
    this.maxIndex = this.items.length - 1;
    
    this.init();
  }
  
  init() {
    this.setupNavigation();
    this.setupIndicators();
    this.setupScrollListener();
    this.updateNavigation();
    this.updateIndicators();
    
    // Set initial scroll position
    this.scrollToIndex(0);
  }
  
  setupNavigation() {
    this.prevBtn.addEventListener('click', () => {
      this.scrollToPrevious();
    });
    
    this.nextBtn.addEventListener('click', () => {
      this.scrollToNext();
    });
  }
  
  setupIndicators() {
    this.indicators.forEach((indicator, index) => {
      indicator.addEventListener('click', () => {
        this.scrollToIndex(index);
      });
    });
  }
  
  setupScrollListener() {
    let scrollTimeout;
    
    this.container.addEventListener('scroll', () => {
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        this.updateCurrentIndex();
        this.updateNavigation();
        this.updateIndicators();
      }, 100);
    });
  }
  
  scrollToPrevious() {
    if (this.currentIndex > 0) {
      this.scrollToIndex(this.currentIndex - 1);
    }
  }
  
  scrollToNext() {
    if (this.currentIndex < this.maxIndex) {
      this.scrollToIndex(this.currentIndex + 1);
    }
  }
  
  scrollToIndex(index) {
    if (index < 0 || index > this.maxIndex) return;
    
    this.currentIndex = index;
    const scrollLeft = index * this.itemWidth;
    
    this.container.scrollTo({
      left: scrollLeft,
      behavior: 'smooth'
    });
    
    this.updateNavigation();
    this.updateIndicators();
  }
  
  updateCurrentIndex() {
    const scrollLeft = this.container.scrollLeft;
    const newIndex = Math.round(scrollLeft / this.itemWidth);
    this.currentIndex = Math.max(0, Math.min(newIndex, this.maxIndex));
  }
  
  updateNavigation() {
    this.prevBtn.disabled = this.currentIndex === 0;
    this.nextBtn.disabled = this.currentIndex === this.maxIndex;
  }
  
  updateIndicators() {
    this.indicators.forEach((indicator, index) => {
      indicator.classList.toggle('active', index === this.currentIndex);
    });
  }
  
  // Public method to get current item info
  getCurrentItemInfo() {
    return {
      index: this.currentIndex,
      total: this.items.length,
      item: this.items[this.currentIndex]
    };
  }
  
  // Public method to auto-play gallery
  startAutoPlay(interval = 3000) {
    this.autoPlayInterval = setInterval(() => {
      if (this.currentIndex < this.maxIndex) {
        this.scrollToNext();
      } else {
        this.scrollToIndex(0);
      }
    }, interval);
  }
  
  stopAutoPlay() {
    if (this.autoPlayInterval) {
      clearInterval(this.autoPlayInterval);
      this.autoPlayInterval = null;
    }
  }
  
  // Handle keyboard navigation
  handleKeyboard(event) {
    switch (event.key) {
      case 'ArrowLeft':
        event.preventDefault();
        this.scrollToPrevious();
        break;
      case 'ArrowRight':
        event.preventDefault();
        this.scrollToNext();
        break;
      case 'Home':
        event.preventDefault();
        this.scrollToIndex(0);
        break;
      case 'End':
        event.preventDefault();
        this.scrollToIndex(this.maxIndex);
        break;
    }
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Wait a bit to ensure all elements are rendered
  setTimeout(() => {
    window.scrollSnapGallery = new ScrollSnapGallery();
    
    // Add keyboard navigation
    document.addEventListener('keydown', (event) => {
      if (window.scrollSnapGallery && 
          document.activeElement && 
          document.activeElement.closest('.scroll-snap-demo')) {
        window.scrollSnapGallery.handleKeyboard(event);
      }
    });
  }, 100);
});

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ScrollSnapGallery;
}
