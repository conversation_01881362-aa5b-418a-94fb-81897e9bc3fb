/* ===================================
   ERROR SUPPRESSOR FOR KNOWN ISSUES
   ================================= */

class ErrorSuppressor {
  constructor() {
    this.suppressedErrors = new Set();
    this.knownIssues = [
      // Duplicate identifier warnings (expected in modular architecture)
      'already been declared',
      'has already been declared',
      "Identifier 'ScrollSnapGallery' has already been declared",
      "Identifier 'ThemeSystem' has already been declared",
      "Identifier 'HoudiniDemo' has already been declared",
      "ScrollSnapGallery' has already been declared",
      "ThemeSystem' has already been declared",
      "HoudiniDemo' has already been declared",

      // TypeScript-like warnings that don't affect functionality
      "Property 'themeSystem' may not exist",
      "Property 'houdiniDemo' may not exist",
      "Property 'scrollSnapGallery' may not exist",
      "Property 'clipboardManager' may not exist",
      "Property 'codeToggle' may not exist",
      "Property 'swManager' may not exist",
      "Property 'resourceOptimizer' may not exist",
      "Property 'imageOptimizer' may not exist",
      "Property 'errorHandler' may not exist",

      // Syntax errors that are actually warnings
      'Uncaught SyntaxError: Identifier',
      'SyntaxError: Identifier',
      'Uncaught SyntaxError',

      // Deprecated API warnings (still functional)
      'document.execCommand',
      'is deprecated',

      // Unused variable warnings (not critical)
      'is declared but its value is never read',
      'never read',

      // CDN resource failures (have fallbacks)
      'ERR_ABORTED 404',
      'Not Found',
      'Failed to load resource',

      // External resource timeouts (non-critical)
      'fonts.googleapis.com',
      'cdnjs.cloudflare.com',
    ];

    this.init();
  }

  init() {
    this.interceptConsoleErrors();
    this.setupResourceErrorSuppression();
    console.log(
      '🔇 Error suppressor initialized for known non-critical issues'
    );
  }

  interceptConsoleErrors() {
    // Store original console methods
    const originalError = console.error;
    const originalWarn = console.warn;
    const originalLog = console.log;

    // Override console.error
    console.error = (...args) => {
      const message = args.join(' ');
      if (!this.shouldSuppressError(message)) {
        originalError.apply(console, args);
      } else {
        this.suppressedErrors.add(message);
        // Use original log to avoid recursion
        originalLog(
          '🔇 Suppressed known error:',
          message.substring(0, 100) + '...'
        );
      }
    };

    // Override console.warn for specific warnings
    console.warn = (...args) => {
      const message = args.join(' ');
      if (!this.shouldSuppressWarning(message)) {
        originalWarn.apply(console, args);
      } else {
        this.suppressedErrors.add(message);
        // Use original log to avoid recursion
        originalLog(
          '🔇 Suppressed known warning:',
          message.substring(0, 100) + '...'
        );
      }
    };

    // Store references for potential restoration
    this.originalConsole = {
      error: originalError,
      warn: originalWarn,
      log: originalLog,
    };
  }

  setupResourceErrorSuppression() {
    // Suppress network errors for external resources and script errors
    window.addEventListener(
      'error',
      (event) => {
        // Handle script errors
        if (event.error && event.error.message) {
          const errorMessage = event.error.message;
          if (this.shouldSuppressError(errorMessage)) {
            event.preventDefault();
            event.stopPropagation();
            this.suppressedErrors.add(errorMessage);
            this.originalConsole.log(
              '🔇 Suppressed script error:',
              errorMessage.substring(0, 100) + '...'
            );
            return false;
          }
        }

        // Handle resource loading errors
        if (event.target && (event.target.src || event.target.href)) {
          const resourceUrl = event.target.src || event.target.href;

          if (this.isKnownExternalResource(resourceUrl)) {
            event.preventDefault();
            event.stopPropagation();
            this.originalConsole.log(
              '🔇 Suppressed external resource error:',
              resourceUrl
            );
            return false;
          }
        }
      },
      true
    );

    // Also handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      const errorMessage = event.reason
        ? event.reason.toString()
        : 'Unknown promise rejection';
      if (this.shouldSuppressError(errorMessage)) {
        event.preventDefault();
        this.suppressedErrors.add(errorMessage);
        this.originalConsole.log(
          '🔇 Suppressed promise rejection:',
          errorMessage.substring(0, 100) + '...'
        );
      }
    });
  }

  shouldSuppressError(message) {
    return this.knownIssues.some((issue) =>
      message.toLowerCase().includes(issue.toLowerCase())
    );
  }

  shouldSuppressWarning(message) {
    // More lenient for warnings
    return (
      this.knownIssues.some((issue) =>
        message.toLowerCase().includes(issue.toLowerCase())
      ) || message.includes('⚠️')
    ); // Suppress our own warning messages
  }

  isKnownExternalResource(url) {
    const externalDomains = [
      'fonts.googleapis.com',
      'cdnjs.cloudflare.com',
      'fonts.gstatic.com',
    ];

    return externalDomains.some((domain) => url.includes(domain));
  }

  addKnownIssue(pattern) {
    this.knownIssues.push(pattern);
  }

  removeKnownIssue(pattern) {
    const index = this.knownIssues.indexOf(pattern);
    if (index > -1) {
      this.knownIssues.splice(index, 1);
    }
  }

  getSuppressedErrors() {
    return Array.from(this.suppressedErrors);
  }

  getKnownIssues() {
    return [...this.knownIssues];
  }

  clearSuppressedErrors() {
    this.suppressedErrors.clear();
  }

  // Temporarily disable suppression for debugging
  disableSuppression() {
    this.suppressionEnabled = false;
    console.log('🔊 Error suppression temporarily disabled');
  }

  enableSuppression() {
    this.suppressionEnabled = true;
    console.log('🔇 Error suppression re-enabled');
  }

  getStats() {
    return {
      totalSuppressed: this.suppressedErrors.size,
      knownIssuesCount: this.knownIssues.length,
      suppressionEnabled: this.suppressionEnabled !== false,
    };
  }
}

// Initialize error suppressor immediately (before other scripts)
if (!window.errorSuppressor) {
  window.errorSuppressor = new ErrorSuppressor();
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ErrorSuppressor;
}
