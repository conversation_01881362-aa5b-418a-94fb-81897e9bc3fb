/* ===================================
   NEON CYBERPUNK THEME
   ================================= */

/* CSS Custom Properties */
:root {
  /* Neon Primary Colors */
  --neon-cyan: #00ffff;
  --neon-purple: #8b5cf6;
  --neon-pink: #ff0080;
  --neon-green: #00ff41;

  /* Background Colors */
  --bg-dark: #0a0a0a;
  --bg-medium: #1a1a2e;
  --bg-light: #16213e;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --text-accent: #00ffff;

  /* Effects */
  --glow-cyan: 0 0 20px #00ffff;
  --glow-purple: 0 0 20px #8b5cf6;
  --glow-pink: 0 0 20px #ff0080;
  --glow-green: 0 0 20px #00ff41;

  /* Gradients */
  --gradient-neon: linear-gradient(45deg, var(--neon-cyan), var(--neon-purple), var(--neon-pink));
  --gradient-bg: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-medium) 100%);
}

/* Neon Text Effects */
.neon-title {
  background: var(--gradient-neon);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 
    0 0 10px rgba(0, 255, 255, 0.5),
    0 0 20px rgba(0, 255, 255, 0.3),
    0 0 40px rgba(0, 255, 255, 0.1);
  animation: textGlow 3s ease-in-out infinite alternate;
}

/* Neon Button Styles */
.btn-show-code {
  background: transparent;
  border: 2px solid var(--neon-cyan);
  color: var(--neon-cyan);
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 1px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
}

.btn-show-code::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-show-code:hover::before {
  left: 100%;
}

.btn-show-code:hover {
  background: var(--neon-cyan);
  color: var(--bg-dark);
  box-shadow: var(--glow-cyan);
  transform: translateY(-2px);
}

.btn-show-code:active {
  transform: translateY(0);
}

/* Copy Button */
.btn-copy {
  background: transparent;
  border: 1px solid var(--neon-green);
  color: var(--neon-green);
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.8rem;
  text-transform: uppercase;
  font-weight: 600;
}

.btn-copy:hover {
  background: var(--neon-green);
  color: var(--bg-dark);
  box-shadow: var(--glow-green);
}

/* Code Panel Styles */
.code-panel {
  background: rgba(0, 0, 0, 0.8);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 0 0 12px 12px;
  overflow: hidden;
  margin: 0 -2rem -2rem -2rem;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.5);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.code-label {
  color: var(--neon-cyan);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.8rem;
  letter-spacing: 1px;
}

.code-content {
  padding: 2rem;
  margin: 0;
  background: transparent;
  color: var(--text-primary);
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.9rem;
  line-height: 1.6;
  overflow-x: auto;
}

/* Animations */
@keyframes neonPulse {
  0%, 100% {
    opacity: 0.7;
    filter: blur(0px);
  }
  50% {
    opacity: 1;
    filter: blur(1px);
  }
}

@keyframes textGlow {
  0% {
    text-shadow: 
      0 0 10px rgba(0, 255, 255, 0.5),
      0 0 20px rgba(0, 255, 255, 0.3),
      0 0 40px rgba(0, 255, 255, 0.1);
  }
  100% {
    text-shadow: 
      0 0 20px rgba(0, 255, 255, 0.8),
      0 0 30px rgba(0, 255, 255, 0.5),
      0 0 60px rgba(0, 255, 255, 0.2);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-20px);
  }
}

/* Scrollbar Customization */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--neon-cyan);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neon-purple);
}

/* Selection Styles */
::selection {
  background: var(--neon-cyan);
  color: var(--bg-dark);
}

::-moz-selection {
  background: var(--neon-cyan);
  color: var(--bg-dark);
}
