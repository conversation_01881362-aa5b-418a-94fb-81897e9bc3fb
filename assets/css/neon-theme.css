/* ===================================
   NEON CYBERPUNK THEME
   ================================= */

/* CSS Custom Properties */
:root {
  /* Neon Primary Colors */
  --neon-cyan: #00ffff;
  --neon-purple: #8b5cf6;
  --neon-pink: #ff0080;
  --neon-green: #00ff41;
  --neon-blue: #0066ff;
  --neon-orange: #ff6600;

  /* Background Colors */
  --bg-dark: #0a0a0a;
  --bg-medium: #1a1a2e;
  --bg-light: #16213e;
  --bg-card: #1e1e2e;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --text-accent: #00ffff;
  --text-muted: #666666;

  /* Enhanced Glow Effects */
  --glow-cyan: 0 0 20px #00ffff, 0 0 40px #00ffff, 0 0 60px #00ffff;
  --glow-purple: 0 0 20px #8b5cf6, 0 0 40px #8b5cf6, 0 0 60px #8b5cf6;
  --glow-pink: 0 0 20px #ff0080, 0 0 40px #ff0080, 0 0 60px #ff0080;
  --glow-green: 0 0 20px #00ff41, 0 0 40px #00ff41, 0 0 60px #00ff41;

  /* Subtle Glows */
  --glow-cyan-soft: 0 0 10px rgba(0, 255, 255, 0.3);
  --glow-purple-soft: 0 0 10px rgba(139, 92, 246, 0.3);
  --glow-pink-soft: 0 0 10px rgba(255, 0, 128, 0.3);
  --glow-green-soft: 0 0 10px rgba(0, 255, 65, 0.3);

  /* Advanced Gradients */
  --gradient-neon: linear-gradient(
    45deg,
    var(--neon-cyan),
    var(--neon-purple),
    var(--neon-pink)
  );
  --gradient-neon-animated: linear-gradient(
    45deg,
    var(--neon-cyan),
    var(--neon-purple),
    var(--neon-pink),
    var(--neon-green)
  );
  --gradient-bg: linear-gradient(
    135deg,
    var(--bg-dark) 0%,
    var(--bg-medium) 100%
  );
  --gradient-card: linear-gradient(145deg, var(--bg-medium), var(--bg-light));
  --gradient-border: linear-gradient(
    45deg,
    var(--neon-cyan),
    var(--neon-purple),
    var(--neon-pink)
  );

  /* Animation Properties */
  --animation-speed: 1s;
  --animation-speed-fast: 0.3s;
  --animation-speed-slow: 2s;

  /* Border Radius */
  --border-radius: 16px;
  --border-radius-small: 8px;
  --border-radius-large: 24px;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
}

/* Enhanced Body Background */
body {
  background: var(--bg-dark);
  background-image: radial-gradient(
      circle at 20% 80%,
      rgba(0, 255, 255, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(139, 92, 246, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 40%,
      rgba(255, 0, 128, 0.05) 0%,
      transparent 50%
    );
  background-attachment: fixed;
  position: relative;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
      45deg,
      transparent 30%,
      rgba(0, 255, 255, 0.02) 50%,
      transparent 70%
    ),
    linear-gradient(
      -45deg,
      transparent 30%,
      rgba(139, 92, 246, 0.02) 50%,
      transparent 70%
    );
  pointer-events: none;
  z-index: -1;
  animation: backgroundShift 20s ease-in-out infinite;
}

/* Advanced Neon Text Effects */
.neon-title {
  background: var(--gradient-neon);
  background-size: 200% 200%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5), 0 0 20px rgba(0, 255, 255, 0.3),
    0 0 40px rgba(0, 255, 255, 0.1), 0 0 80px rgba(0, 255, 255, 0.05);
  animation: textGlow 3s ease-in-out infinite alternate,
    gradientShift 8s ease-in-out infinite;
  position: relative;
}

.neon-title::before {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-neon);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  opacity: 0.3;
  filter: blur(2px);
  z-index: -1;
}

/* Hero Header Enhanced */
.hero-header {
  position: relative;
  overflow: hidden;
}

.hero-header::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    from 0deg,
    transparent,
    rgba(0, 255, 255, 0.1),
    transparent,
    rgba(139, 92, 246, 0.1),
    transparent
  );
  transform: translate(-50%, -50%) rotate(0deg);
  animation: rotate 30s linear infinite;
  z-index: -1;
}

.hero-description {
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
  animation: subtleGlow 4s ease-in-out infinite alternate;
}

/* Enhanced Neon Button Styles */
.btn-show-code {
  background: transparent;
  border: 2px solid var(--neon-cyan);
  color: var(--neon-cyan);
  padding: clamp(12px, 3vw, 16px) clamp(20px, 5vw, 32px);
  border-radius: var(--border-radius-small);
  cursor: pointer;
  transition: all var(--animation-speed-fast) ease;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 1px;
  font-size: clamp(0.8rem, 2.5vw, 1rem);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
  font-family: 'Nunito Sans', sans-serif;
  will-change: transform;
  z-index: 10;
  min-width: clamp(140px, 35vw, 180px);
  min-height: 48px;
  box-sizing: border-box;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.btn-show-code::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 255, 255, 0.4),
    transparent
  );
  transition: left 0.6s ease;
  z-index: -1;
}

.btn-show-code::after {
  content: '';
  position: absolute;
  inset: -2px;
  background: var(--neon-cyan);
  border-radius: var(--border-radius-small);
  opacity: 0;
  transition: opacity var(--animation-speed-fast) ease;
  z-index: -2;
  filter: blur(8px);
}

.btn-show-code:hover::before {
  left: 100%;
}

.btn-show-code:hover::after {
  opacity: 0.5;
}

.btn-show-code:hover {
  background: var(--neon-cyan);
  color: var(--bg-dark);
  box-shadow: var(--glow-cyan-soft);
  transform: translateY(-3px) scale(1.02);
  border-color: transparent;
}

.btn-show-code:active {
  transform: translateY(-1px) scale(1);
}

.btn-show-code.active {
  background: var(--neon-purple);
  border-color: var(--neon-purple);
  color: var(--text-primary);
  box-shadow: var(--glow-purple-soft);
}

/* Enhanced Icon Animation */
.btn-icon {
  transition: transform var(--animation-speed-fast) ease;
  display: inline-block;
}

.btn-show-code:hover .btn-icon {
  transform: rotate(180deg) scale(1.2);
}

.btn-show-code.active .btn-icon {
  transform: rotate(90deg);
}

/* Enhanced Copy Button */
.btn-copy {
  background: transparent;
  border: 1px solid var(--neon-green);
  color: var(--neon-green);
  padding: clamp(10px, 2.5vw, 14px) clamp(16px, 4vw, 24px);
  border-radius: var(--border-radius-small);
  cursor: pointer;
  transition: all var(--animation-speed-fast) ease;
  font-size: clamp(0.75rem, 2vw, 0.9rem);
  text-transform: uppercase;
  font-weight: 600;
  font-family: 'Nunito Sans', sans-serif;
  position: relative;
  overflow: hidden;
  min-height: 44px;
  min-width: clamp(80px, 20vw, 120px);
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.btn-copy::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: var(--neon-green);
  border-radius: 50%;
  transition: all var(--animation-speed-fast) ease;
  transform: translate(-50%, -50%);
  z-index: -1;
}

.btn-copy:hover::before {
  width: 200%;
  height: 200%;
}

.btn-copy:hover {
  color: var(--bg-dark);
  box-shadow: var(--glow-green-soft);
  transform: scale(1.05);
}

.btn-copy.copy-success {
  border-color: var(--neon-green);
  animation: copySuccess 0.6s ease;
}

.btn-copy.copy-error {
  border-color: var(--neon-pink);
  color: var(--neon-pink);
  animation: copyError 0.6s ease;
}

/* Code Panel Styles */
.code-panel {
  background: rgba(0, 0, 0, 0.8);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 0 0 12px 12px;
  overflow: hidden;
  margin: 0 -2rem -2rem -2rem;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.5);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.code-label {
  color: var(--neon-cyan);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.8rem;
  letter-spacing: 1px;
}

.code-content {
  padding: 2rem;
  margin: 0;
  background: transparent;
  color: var(--text-primary);
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.9rem;
  line-height: 1.6;
  overflow-x: auto;
}

/* Animations */
@keyframes neonPulse {
  0%,
  100% {
    opacity: 0.7;
    filter: blur(0px);
  }
  50% {
    opacity: 1;
    filter: blur(1px);
  }
}

@keyframes textGlow {
  0% {
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5),
      0 0 20px rgba(0, 255, 255, 0.3), 0 0 40px rgba(0, 255, 255, 0.1);
  }
  100% {
    text-shadow: 0 0 20px rgba(0, 255, 255, 0.8),
      0 0 30px rgba(0, 255, 255, 0.5), 0 0 60px rgba(0, 255, 255, 0.2);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-20px);
  }
}

/* Advanced Animations */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes backgroundShift {
  0%,
  100% {
    transform: translateX(0) translateY(0);
  }
  25% {
    transform: translateX(10px) translateY(-5px);
  }
  50% {
    transform: translateX(-5px) translateY(10px);
  }
  75% {
    transform: translateX(-10px) translateY(-10px);
  }
}

@keyframes rotate {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes subtleGlow {
  0% {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
  }
  100% {
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.6);
  }
}

@keyframes cardGlow {
  0% {
    filter: blur(20px);
    opacity: 0.3;
  }
  100% {
    filter: blur(30px);
    opacity: 0.5;
  }
}

/* Gentler Card Animations */
@keyframes neonPulseGentle {
  0%,
  100% {
    opacity: 0.4;
    filter: blur(0px);
  }
  50% {
    opacity: 0.6;
    filter: blur(0.5px);
  }
}

@keyframes cardGlowGentle {
  0% {
    filter: blur(20px);
    opacity: 0.15;
  }
  100% {
    filter: blur(25px);
    opacity: 0.25;
  }
}

@keyframes copySuccess {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
    box-shadow: var(--glow-green);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes copyError {
  0% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
  100% {
    transform: translateX(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes floatUp {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
  }
  20% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  80% {
    opacity: 1;
    transform: translateX(-50%) translateY(-10px);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
}

/* Intersection Observer Animation Classes */
.animate-in {
  animation: fadeInUp 0.8s ease-out forwards;
}

/* Performance Optimizations */
.magic-card,
.btn-show-code,
.btn-copy {
  will-change: transform;
}

/* Mobile-First Responsive Button Styles */
/* Base styles are already mobile-optimized above */

/* Small screens and up */
@media (min-width: 480px) {
  .btn-show-code {
    min-width: 160px;
    font-size: 0.9rem;
  }

  .btn-copy {
    min-width: 100px;
    font-size: 0.8rem;
  }
}

/* Medium screens and up */
@media (min-width: 768px) {
  .btn-show-code {
    padding: 14px 28px;
    font-size: 1rem;
    min-width: 180px;
  }

  .btn-copy {
    padding: 10px 20px;
    font-size: 0.9rem;
    min-width: 110px;
  }
}

@media (min-width: 1280px) {
  .btn-show-code {
    padding: 16px 32px;
    font-size: 1.1rem;
    min-width: 200px;
    min-height: 52px;
    position: relative;
    z-index: 20;
  }

  .btn-copy {
    padding: 12px 24px;
    font-size: 1rem;
    min-width: 120px;
  }

  /* Ensure buttons are always visible on large screens */
  .card-controls {
    position: relative;
    z-index: 20;
    margin: 2rem 0;
  }
}

@media (min-width: 1600px) {
  .btn-show-code {
    padding: 18px 36px;
    font-size: 1.2rem;
    min-width: 220px;
    min-height: 56px;
  }

  .btn-copy {
    padding: 14px 28px;
    font-size: 1.1rem;
    min-width: 140px;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .neon-title {
    animation: none;
  }

  .hero-header::after {
    animation: none;
  }

  .magic-card:hover::before,
  .magic-card:hover::after {
    animation: none;
  }
}

/* Scrollbar Customization */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--neon-cyan);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neon-purple);
}

/* Selection Styles */
::selection {
  background: var(--neon-cyan);
  color: var(--bg-dark);
}

::-moz-selection {
  background: var(--neon-cyan);
  color: var(--bg-dark);
}
