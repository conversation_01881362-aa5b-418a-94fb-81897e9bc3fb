/* ===================================
   CSS GRID MAGIC TRICK
   ================================= */

/* Grid Layouts Demo Container */
.grid-layouts-demo {
  position: relative;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 400px;
}

/* Holy Grail Layout */
.holy-grail-layout {
  display: grid;
  grid-template-areas:
    'header header header'
    'sidebar main aside'
    'footer footer footer';
  grid-template-rows: auto 1fr auto;
  grid-template-columns: 200px 1fr 150px;
  gap: 1rem;
  min-height: 300px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  padding: 1rem;
}

.grid-item {
  background: linear-gradient(135deg, var(--neon-cyan), var(--neon-purple));
  border-radius: 6px;
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  font-weight: 600;
  transition: all 0.3s ease;
}

.grid-item:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 15px rgba(0, 255, 255, 0.3);
}

.grid-header {
  grid-area: header;
}
.grid-sidebar {
  grid-area: sidebar;
}
.grid-main {
  grid-area: main;
}
.grid-aside {
  grid-area: aside;
}
.grid-footer {
  grid-area: footer;
}

/* Responsive Grid */
@media (max-width: 768px) {
  .holy-grail-layout {
    grid-template-areas:
      'header'
      'main'
      'sidebar'
      'aside'
      'footer';
    grid-template-columns: 1fr;
  }
}
