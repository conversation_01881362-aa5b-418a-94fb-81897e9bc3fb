/* ===================================
   CSS TOOLTIPS TRICK
   ================================= */

/* Tooltips Demo Container */
.tooltips-demo {
  position: relative;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 350px;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Demo Instructions */
.tooltip-instructions {
  text-align: center;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: rgba(0, 255, 255, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(0, 255, 255, 0.2);
}

.tooltip-instructions p {
  margin: 0;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.tooltip-instructions strong {
  color: var(--neon-cyan);
}

/* Tooltip Grid Layout */
.tooltip-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  align-items: center;
  justify-items: center;
  flex: 1;
}

/* Base Tooltip Styles */
.tooltip {
  position: relative;
  display: inline-block;
  cursor: pointer;
  transition: all 0.3s ease;
}

/* Tooltip Buttons */
.tooltip-btn {
  background: linear-gradient(135deg, var(--neon-cyan), var(--neon-purple));
  color: var(--text-primary);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Nunito Sans', sans-serif;
  position: relative;
  overflow: hidden;
}

.tooltip-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.tooltip-btn:hover::before {
  left: 100%;
}

.tooltip-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 255, 255, 0.3);
}

/* Tooltip Content Base */
.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  background: linear-gradient(135deg, var(--bg-dark), var(--bg-medium));
  color: var(--text-primary);
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
  z-index: 1000;
  border: 1px solid var(--neon-cyan);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 255, 255, 0.2);
  font-family: 'Nunito Sans', sans-serif;
}

/* Tooltip Arrow Base */
.tooltip::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
  z-index: 1001;
}

/* Show tooltip on hover */
.tooltip:hover::after,
.tooltip:hover::before {
  opacity: 1;
}

/* Top Tooltip */
.tooltip-top::after {
  bottom: calc(100% + 8px);
  left: 50%;
  transform: translateX(-50%) translateY(10px);
}

.tooltip-top::before {
  bottom: calc(100% + 2px);
  left: 50%;
  transform: translateX(-50%);
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid var(--neon-cyan);
}

.tooltip-top:hover::after {
  transform: translateX(-50%) translateY(0);
}

/* Bottom Tooltip */
.tooltip-bottom::after {
  top: calc(100% + 8px);
  left: 50%;
  transform: translateX(-50%) translateY(-10px);
}

.tooltip-bottom::before {
  top: calc(100% + 2px);
  left: 50%;
  transform: translateX(-50%);
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid var(--neon-cyan);
}

.tooltip-bottom:hover::after {
  transform: translateX(-50%) translateY(0);
}

/* Left Tooltip */
.tooltip-left::after {
  right: calc(100% + 8px);
  top: 50%;
  transform: translateY(-50%) translateX(10px);
}

.tooltip-left::before {
  right: calc(100% + 2px);
  top: 50%;
  transform: translateY(-50%);
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-left: 6px solid var(--neon-cyan);
}

.tooltip-left:hover::after {
  transform: translateY(-50%) translateX(0);
}

/* Right Tooltip */
.tooltip-right::after {
  left: calc(100% + 8px);
  top: 50%;
  transform: translateY(-50%) translateX(-10px);
}

.tooltip-right::before {
  left: calc(100% + 2px);
  top: 50%;
  transform: translateY(-50%);
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-right: 6px solid var(--neon-cyan);
}

.tooltip-right:hover::after {
  transform: translateY(-50%) translateX(0);
}

/* Delayed Tooltips */
.tooltip-delay::after,
.tooltip-delay::before {
  transition-delay: 0.5s;
}

.tooltip-delay-long::after,
.tooltip-delay-long::before {
  transition-delay: 1s;
}

/* Animated Tooltips */
.tooltip-bounce:hover::after {
  animation: tooltipBounce 0.6s ease;
}

.tooltip-fade::after {
  transition: all 0.6s ease;
}

.tooltip-slide::after {
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Special Effect Tooltips */
.tooltip-glow::after {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3), 0 0 20px var(--neon-cyan),
    0 0 40px var(--neon-cyan);
  animation: glowPulse 2s ease-in-out infinite alternate;
}

.tooltip-rainbow::after {
  background: linear-gradient(
    45deg,
    var(--neon-cyan),
    var(--neon-purple),
    var(--neon-pink),
    var(--neon-green)
  );
  background-size: 300% 300%;
  animation: rainbowShift 3s ease infinite;
}

/* Multiline Tooltips */
.tooltip-multiline::after {
  white-space: pre-line;
  max-width: 200px;
  text-align: center;
  line-height: 1.4;
}

/* Tooltip Animations */
@keyframes tooltipBounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  40%,
  43% {
    transform: translateX(-50%) translateY(-10px);
  }
  70% {
    transform: translateX(-50%) translateY(-5px);
  }
  90% {
    transform: translateX(-50%) translateY(-2px);
  }
}

@keyframes glowPulse {
  0% {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3), 0 0 20px var(--neon-cyan),
      0 0 40px var(--neon-cyan);
  }
  100% {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3), 0 0 30px var(--neon-cyan),
      0 0 60px var(--neon-cyan);
  }
}

@keyframes rainbowShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Demo Section Styles */
.tooltip-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.tooltip-section-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--neon-cyan);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 0.5rem;
}

.tooltip-section-description {
  font-size: 0.8rem;
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tooltip-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .tooltips-demo {
    padding: 1.5rem;
    min-height: 300px;
  }

  .tooltip::after {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    max-width: 150px;
    white-space: normal;
    text-align: center;
  }

  .tooltip-multiline::after {
    max-width: 120px;
  }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .tooltip:active::after,
  .tooltip:active::before {
    opacity: 1;
  }

  .tooltip:active::after {
    transform: translateX(-50%) translateY(0) !important;
  }

  .tooltip-instructions {
    background: rgba(255, 165, 0, 0.1);
    border-color: rgba(255, 165, 0, 0.2);
  }

  .tooltip-instructions strong {
    color: #ffa500;
  }
}
