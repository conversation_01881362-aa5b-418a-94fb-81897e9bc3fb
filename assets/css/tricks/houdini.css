/* ===================================
   CSS HOUDINI TRICK
   ================================= */

/* <PERSON><PERSON>ni Demo Container */
.houdini-demo {
  position: relative;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 400px;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Houdini Instructions */
.houdini-instructions {
  text-align: center;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.houdini-instructions p {
  margin: 0;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.houdini-instructions strong {
  color: var(--neon-purple);
}

/* <PERSON><PERSON><PERSON> */
.houdini-canvas {
  width: 100%;
  height: 200px;
  background: linear-gradient(45deg, var(--neon-purple), var(--neon-cyan));
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  margin-bottom: 2rem;
}

/* Simulated Paint API Effect */
.houdini-canvas::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
      rgba(255, 255, 255, 0.3) 0%,
      transparent 50%
    ),
    linear-gradient(
      var(--rotation, 45deg),
      var(--color-1, #8b5cf6) 0%,
      var(--color-2, #00ffff) 100%
    );
  opacity: var(--opacity, 0.8);
  transition: all 0.3s ease;
  border-radius: 12px;
}

/* Controls */
.houdini-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.control-group {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.control-label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 0.9rem;
}

.control-slider {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  outline: none;
  appearance: none;
  cursor: pointer;
  margin-bottom: 0.5rem;
}

.control-slider::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  background: var(--neon-purple);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
  transition: all 0.3s ease;
}

.control-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 0 15px rgba(139, 92, 246, 0.8);
}

.control-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: var(--neon-purple);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}

.control-value {
  font-size: 0.8rem;
  color: var(--neon-purple);
  font-weight: 600;
  text-align: center;
}

/* Color Picker */
.color-picker {
  width: 100%;
  height: 40px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  background: transparent;
}

.color-picker::-webkit-color-swatch {
  border: 2px solid var(--neon-purple);
  border-radius: 6px;
}

.color-picker::-moz-color-swatch {
  border: 2px solid var(--neon-purple);
  border-radius: 6px;
}

/* Houdini Features */
.houdini-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
}

.feature-card {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
  text-align: center;
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.feature-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--neon-purple);
  margin-bottom: 0.5rem;
}

.feature-description {
  font-size: 0.8rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* Browser Support Notice */
.browser-notice {
  background: rgba(255, 165, 0, 0.1);
  border: 1px solid rgba(255, 165, 0, 0.3);
  border-radius: 6px;
  padding: 1rem;
  margin-top: 1rem;
  text-align: center;
}

.browser-notice p {
  margin: 0;
  color: #ffa500;
  font-size: 0.85rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .houdini-demo {
    padding: 1.5rem;
  }

  .houdini-controls {
    grid-template-columns: 1fr;
  }

  .houdini-features {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .houdini-canvas {
    height: 150px;
    font-size: 1.2rem;
  }
}
