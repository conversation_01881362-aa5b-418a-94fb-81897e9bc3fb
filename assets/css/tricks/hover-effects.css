/* ===================================
   MAGIC HOVER EFFECTS TRICK
   ================================= */

/* Hover Effects Demo Container */
.hover-effects-demo {
  position: relative;
  padding: clamp(1rem, 3vw, 2.5rem);
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: clamp(300px, 50vw, 450px);
  display: flex;
  flex-direction: column;
  gap: clamp(1rem, 3vw, 2.5rem);
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

/* Demo Instructions */
.hover-instructions {
  text-align: center;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.hover-instructions p {
  margin: 0;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.hover-instructions strong {
  color: var(--neon-purple);
}

/* Effects Grid Layout */
.effects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  align-items: center;
  justify-items: center;
  flex: 1;
  max-width: 100%;
  overflow: hidden;
}

/* Base Button Styles */
.effect-btn {
  padding: clamp(0.75rem, 3vw, 1.25rem) clamp(1.5rem, 5vw, 2.5rem);
  border: none;
  border-radius: 12px;
  font-size: clamp(0.85rem, 2.5vw, 1.1rem);
  font-weight: 600;
  font-family: 'Nunito Sans', sans-serif;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  min-width: clamp(140px, 35vw, 200px);
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* Effect 1: Liquid Morphing */
.liquid-btn {
  background: linear-gradient(45deg, var(--neon-cyan), var(--neon-purple));
  color: var(--text-primary);
  border-radius: 50px;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
}

.liquid-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, var(--neon-pink), var(--neon-green));
  border-radius: 50px;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  z-index: -1;
}

.liquid-btn:hover {
  transform: scale(1.1) rotate(2deg);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 255, 255, 0.4);
}

.liquid-btn:hover::before {
  opacity: 1;
  transform: scale(1.2) rotate(-2deg);
  border-radius: 30px;
}

/* Effect 2: 3D Flip */
.flip-btn {
  background: linear-gradient(135deg, var(--neon-purple), var(--neon-pink));
  color: var(--text-primary);
  perspective: 1000px;
  transform-style: preserve-3d;
  transition: transform 0.6s ease;
}

.flip-btn::before {
  content: '✨ Flipped!';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--neon-green), var(--neon-cyan));
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: rotateY(180deg);
  backface-visibility: hidden;
  transition: all 0.6s ease;
}

.flip-btn span {
  backface-visibility: hidden;
  transition: all 0.6s ease;
}

.flip-btn:hover {
  transform: rotateY(180deg);
  box-shadow: 0 15px 35px rgba(139, 92, 246, 0.4);
}

/* Effect 3: Glitch */
.glitch-btn {
  background: linear-gradient(45deg, var(--neon-pink), var(--neon-cyan));
  color: var(--text-primary);
  position: relative;
}

.glitch-btn::before,
.glitch-btn::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.glitch-btn::before {
  color: var(--neon-cyan);
  z-index: -1;
}

.glitch-btn::after {
  color: var(--neon-pink);
  z-index: -2;
}

.glitch-btn:hover::before {
  opacity: 0.8;
  transform: translate(-2px, -2px);
  animation: glitch1 0.3s ease-in-out infinite;
}

.glitch-btn:hover::after {
  opacity: 0.8;
  transform: translate(2px, 2px);
  animation: glitch2 0.3s ease-in-out infinite;
}

.glitch-btn:hover {
  animation: glitchMain 0.3s ease-in-out infinite;
}

/* Effect 4: Magnetic */
.magnetic-btn {
  background: linear-gradient(45deg, var(--neon-green), var(--neon-purple));
  color: var(--text-primary);
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  position: relative;
}

.magnetic-btn::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(
    45deg,
    var(--neon-cyan),
    var(--neon-pink),
    var(--neon-green)
  );
  border-radius: 14px;
  opacity: 0;
  transition: all 0.3s ease;
  z-index: -1;
  filter: blur(10px);
}

.magnetic-btn:hover {
  transform: translateY(-8px) scale(1.05);
  box-shadow: 0 20px 40px rgba(0, 255, 65, 0.3);
}

.magnetic-btn:hover::before {
  opacity: 0.7;
  animation: magneticGlow 2s ease-in-out infinite;
}

/* Effect 5: Ripple */
.ripple-btn {
  background: linear-gradient(45deg, var(--neon-cyan), var(--neon-pink));
  color: var(--text-primary);
  position: relative;
  overflow: hidden;
}

.ripple-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.6s ease;
}

.ripple-btn:hover::before {
  width: 300px;
  height: 300px;
}

.ripple-btn:hover {
  box-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
}

/* Effect 6: Neon Glow */
.neon-btn {
  background: transparent;
  border: 2px solid var(--neon-cyan);
  color: var(--neon-cyan);
  position: relative;
  transition: all 0.3s ease;
}

.neon-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--neon-cyan);
  opacity: 0;
  transition: all 0.3s ease;
  z-index: -1;
}

.neon-btn:hover {
  color: var(--bg-dark);
  text-shadow: none;
  box-shadow: 0 0 20px var(--neon-cyan), 0 0 40px var(--neon-cyan),
    0 0 60px var(--neon-cyan);
}

.neon-btn:hover::before {
  opacity: 1;
}

/* Keyframe Animations */
@keyframes glitch1 {
  0%,
  100% {
    transform: translate(-2px, -2px);
  }
  25% {
    transform: translate(-4px, -1px);
  }
  50% {
    transform: translate(-1px, -3px);
  }
  75% {
    transform: translate(-3px, -2px);
  }
}

@keyframes glitch2 {
  0%,
  100% {
    transform: translate(2px, 2px);
  }
  25% {
    transform: translate(1px, 4px);
  }
  50% {
    transform: translate(3px, 1px);
  }
  75% {
    transform: translate(2px, 3px);
  }
}

@keyframes glitchMain {
  0%,
  100% {
    transform: translate(0);
  }
  20% {
    transform: translate(-1px, 1px);
  }
  40% {
    transform: translate(1px, -1px);
  }
  60% {
    transform: translate(-1px, -1px);
  }
  80% {
    transform: translate(1px, 1px);
  }
}

@keyframes magneticGlow {
  0%,
  100% {
    filter: blur(10px);
    opacity: 0.7;
  }
  50% {
    filter: blur(15px);
    opacity: 1;
  }
}

/* Demo Section Styles */
.effect-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.effect-section:hover {
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(255, 255, 255, 0.1);
}

.effect-section-title {
  font-size: 1rem;
  font-weight: 700;
  color: var(--neon-purple);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 0.5rem;
  text-align: center;
}

.effect-section-description {
  font-size: 0.85rem;
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: 1rem;
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
  .effects-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .hover-effects-demo {
    padding: 1.5rem;
    min-height: 350px;
  }

  .effect-btn {
    min-width: 160px;
    min-height: 50px;
    font-size: 0.9rem;
    padding: 0.75rem 1.5rem;
  }

  .effect-section {
    padding: 1rem;
  }

  .effect-section-title {
    font-size: 0.9rem;
  }

  .effect-section-description {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .hover-effects-demo {
    padding: 1rem;
    gap: 1.5rem;
    overflow: hidden;
  }

  .effects-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0;
  }

  .effect-btn {
    min-width: 140px;
    min-height: 45px;
    font-size: 0.8rem;
    padding: 0.6rem 1.2rem;
    max-width: calc(100vw - 4rem);
    box-sizing: border-box;
  }

  .effect-section {
    padding: 0.75rem;
    max-width: 100%;
    box-sizing: border-box;
  }

  .effect-section-title {
    font-size: 0.85rem;
  }

  .effect-section-description {
    font-size: 0.75rem;
  }
}

/* Extra small screens */
@media (max-width: 320px) {
  .hover-effects-demo {
    padding: 0.75rem;
    gap: 1rem;
  }

  .effects-grid {
    gap: 0.75rem;
  }

  .effect-btn {
    min-width: 120px;
    min-height: 40px;
    font-size: 0.75rem;
    padding: 0.5rem 1rem;
    max-width: calc(100vw - 3rem);
  }

  .effect-section {
    padding: 0.5rem;
  }

  .effect-section-title {
    font-size: 0.8rem;
  }

  .effect-section-description {
    font-size: 0.7rem;
  }
}

/* Performance Optimizations */
.effect-btn {
  will-change: transform;
}

.effect-btn:hover {
  will-change: transform, box-shadow;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .effect-btn,
  .effect-btn::before,
  .effect-btn::after {
    transition: none !important;
    animation: none !important;
  }

  .effect-btn:hover {
    transform: none !important;
  }
}

/* Focus States for Keyboard Navigation */
.effect-btn:focus {
  outline: 2px solid var(--neon-cyan);
  outline-offset: 2px;
}

.effect-btn:focus:not(:focus-visible) {
  outline: none;
}
