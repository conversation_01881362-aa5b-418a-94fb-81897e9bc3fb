/* ===================================
   CONTAINER QUERIES TRICK
   ================================= */

/* Container Queries Demo */
.container-queries-demo {
  position: relative;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Resizable Container - Enhanced for Single Column Layout */
.resizable-container {
  container-type: inline-size;
  container-name: demo-container;
  width: 400px;
  min-width: 250px;
  max-width: 800px;
  background: linear-gradient(135deg, var(--bg-medium), var(--bg-light));
  border: 2px solid var(--neon-cyan);
  border-radius: 8px;
  padding: 1.5rem;
  position: relative;
  resize: horizontal;
  overflow: auto;
  margin: 1rem auto;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* Size Indicator */
.size-indicator {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--neon-cyan);
  color: var(--bg-dark);
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  font-family: 'Nunito Sans', monospace;
  box-shadow: var(--glow-cyan-soft);
  z-index: 10;
}

/* Responsive Card Content */
.responsive-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.card-header-demo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.card-avatar {
  width: 40px;
  height: 40px;
  background: var(--neon-purple);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 0.9rem;
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.card-subtitle {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin: 0;
}

.card-content {
  color: var(--text-secondary);
  font-size: 0.8rem;
  line-height: 1.4;
  margin-bottom: 0.75rem;
}

.card-actions {
  display: flex;
  gap: 0.5rem;
}

.card-button {
  background: var(--neon-green);
  color: var(--bg-dark);
  border: none;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.card-button:hover {
  background: var(--neon-cyan);
  transform: translateY(-1px);
}

/* Container Query Breakpoints - Enhanced for Larger Containers */

/* Small container (< 350px) */
@container demo-container (max-width: 349px) {
  .responsive-card {
    padding: 0.75rem;
  }

  .card-header-demo {
    flex-direction: column;
    text-align: center;
    gap: 0.25rem;
  }

  .card-avatar {
    width: 30px;
    height: 30px;
    font-size: 0.8rem;
  }

  .card-title {
    font-size: 0.8rem;
  }

  .card-subtitle {
    font-size: 0.7rem;
  }

  .card-content {
    font-size: 0.75rem;
    text-align: center;
  }

  .card-actions {
    flex-direction: column;
  }

  .card-button {
    font-size: 0.7rem;
    padding: 0.3rem 0.6rem;
  }
}

/* Medium container (350px - 549px) */
@container demo-container (min-width: 350px) and (max-width: 549px) {
  .responsive-card {
    padding: 1rem;
  }

  .card-header-demo {
    flex-direction: row;
  }

  .card-actions {
    flex-direction: row;
  }
}

/* Large container (550px+) */
@container demo-container (min-width: 550px) {
  .responsive-card {
    display: grid;
    grid-template-columns: auto 1fr auto;
    grid-template-areas:
      'avatar info actions'
      'content content content';
    gap: 1rem;
    align-items: start;
  }

  .card-header-demo {
    grid-area: avatar;
    flex-direction: column;
    margin-bottom: 0;
  }

  .card-info {
    grid-area: info;
  }

  .card-content {
    grid-area: content;
    margin-bottom: 0;
  }

  .card-actions {
    grid-area: actions;
    flex-direction: column;
  }

  .card-avatar {
    width: 50px;
    height: 50px;
    font-size: 1rem;
  }

  .card-title {
    font-size: 1rem;
  }

  .card-subtitle {
    font-size: 0.8rem;
  }
}

/* Demo Instructions */
.demo-instructions {
  text-align: center;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: rgba(0, 255, 255, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(0, 255, 255, 0.2);
}

.demo-instructions p {
  margin: 0;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.demo-instructions strong {
  color: var(--neon-cyan);
}

/* Breakpoint Indicators */
.breakpoint-indicators {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
  padding: 0.75rem;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.breakpoint {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.breakpoint::after {
  content: attr(data-size);
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.7rem;
  color: var(--text-secondary);
  white-space: nowrap;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--text-secondary);
  transition: all 0.3s ease;
}

.indicator-label {
  font-size: 0.8rem;
  color: var(--text-secondary);
  font-weight: 600;
  transition: all 0.3s ease;
}

/* Active breakpoint states */
.breakpoint.small.active {
  background: rgba(255, 0, 128, 0.2);
  border: 1px solid var(--neon-pink);
}

.breakpoint.small.active .indicator-dot {
  background: var(--neon-pink);
  box-shadow: 0 0 10px var(--neon-pink);
}

.breakpoint.small.active .indicator-label {
  color: var(--neon-pink);
}

.breakpoint.medium.active {
  background: rgba(0, 255, 255, 0.2);
  border: 1px solid var(--neon-cyan);
}

.breakpoint.medium.active .indicator-dot {
  background: var(--neon-cyan);
  box-shadow: 0 0 10px var(--neon-cyan);
}

.breakpoint.medium.active .indicator-label {
  color: var(--neon-cyan);
}

.breakpoint.large.active {
  background: rgba(139, 92, 246, 0.2);
  border: 1px solid var(--neon-purple);
}

.breakpoint.large.active .indicator-dot {
  background: var(--neon-purple);
  box-shadow: 0 0 10px var(--neon-purple);
}

.breakpoint.large.active .indicator-label {
  color: var(--neon-purple);
}

/* Resize handle enhancement */
.resizable-container::-webkit-resizer {
  background: var(--neon-cyan);
  border-radius: 2px;
}

/* Hover effects */
.resizable-container:hover {
  border-color: var(--neon-purple);
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
}

.resizable-container:hover .size-indicator {
  background: var(--neon-purple);
  box-shadow: var(--glow-purple-soft);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .resizable-container {
    resize: none;
    width: 100%;
    max-width: 100%;
  }

  .demo-instructions {
    font-size: 0.8rem;
  }

  .breakpoint-indicators {
    flex-direction: column;
    gap: 0.5rem;
  }

  .breakpoint {
    justify-content: center;
  }

  .breakpoint::after {
    position: static;
    transform: none;
    margin-left: 0.5rem;
  }
}
