/* ===================================
   FLUID TYPOGRAPHY TRICK
   ================================= */

/* Fluid Typography Demo Container */
.fluid-typography-demo {
  position: relative;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 450px;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Demo Instructions */
.fluid-instructions {
  text-align: center;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: rgba(0, 255, 65, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(0, 255, 65, 0.2);
}

.fluid-instructions p {
  margin: 0;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.fluid-instructions strong {
  color: var(--neon-green);
}

/* Viewport Simulator */
.viewport-simulator {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.simulator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.simulator-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--neon-green);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.viewport-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.viewport-slider {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.viewport-slider label {
  font-size: 0.8rem;
  color: var(--text-secondary);
  min-width: 60px;
}

.viewport-range {
  width: clamp(150px, 40vw, 250px);
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  outline: none;
  appearance: none;
  cursor: pointer;
  touch-action: manipulation;
}

.viewport-range::-webkit-slider-thumb {
  appearance: none;
  width: 24px;
  height: 24px;
  background: var(--neon-green);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 10px rgba(0, 255, 65, 0.5);
  transition: all 0.3s ease;
  touch-action: manipulation;
}

.viewport-range::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 0 15px rgba(0, 255, 65, 0.8);
}

.viewport-range::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: var(--neon-green);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 10px rgba(0, 255, 65, 0.5);
}

.viewport-display {
  font-size: 0.9rem;
  color: var(--neon-green);
  font-weight: 600;
  min-width: 80px;
  text-align: right;
}

/* Preset Buttons */
.viewport-presets {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.preset-btn {
  background: transparent;
  border: 1px solid var(--neon-green);
  color: var(--neon-green);
  padding: clamp(0.5rem, 2vw, 0.75rem) clamp(0.75rem, 3vw, 1rem);
  border-radius: 6px;
  font-size: clamp(0.7rem, 2vw, 0.85rem);
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Nunito Sans', sans-serif;
  min-height: 44px;
  min-width: 60px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.preset-btn:hover,
.preset-btn.active {
  background: var(--neon-green);
  color: var(--bg-dark);
  box-shadow: 0 0 10px rgba(0, 255, 65, 0.3);
}

/* Simulated Viewport */
.simulated-viewport {
  background: rgba(0, 0, 0, 0.4);
  border: 2px solid var(--neon-green);
  border-radius: 8px;
  padding: 2rem;
  margin: 0 auto;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  max-width: 100%;
  box-sizing: border-box;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.simulated-viewport::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--neon-green), var(--neon-cyan));
  border-radius: 8px;
  opacity: 0.3;
  z-index: -1;
  filter: blur(10px);
}

/* Fluid Text Examples */
.fluid-heading {
  font-size: clamp(1.5rem, 5vw, 4rem);
  font-weight: 900;
  color: var(--text-primary);
  margin-bottom: 1rem;
  line-height: 1.1;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.fluid-subheading {
  font-size: clamp(1rem, 3vw, 2rem);
  font-weight: 600;
  color: var(--neon-cyan);
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.fluid-body {
  font-size: clamp(0.9rem, 2vw, 1.2rem);
  color: var(--text-secondary);
  line-height: clamp(1.4, 1.5vw, 1.6);
  max-width: 600px;
}

/* Clamp Visualization */
.clamp-visualization {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.clamp-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--neon-cyan);
  margin-bottom: 1rem;
  text-align: center;
}

.clamp-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.clamp-example {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 6px;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.clamp-label {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  font-family: monospace;
}

.clamp-demo-text {
  color: var(--text-primary);
  margin: 0;
}

.clamp-heading-demo {
  font-size: clamp(1.2rem, 4vw, 2.5rem);
  font-weight: 700;
}

.clamp-body-demo {
  font-size: clamp(0.8rem, 2vw, 1.1rem);
  line-height: clamp(1.3, 1.4vw, 1.5);
}

.clamp-caption-demo {
  font-size: clamp(0.7rem, 1.5vw, 0.9rem);
  opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
  .fluid-typography-demo {
    padding: 1.5rem;
    min-height: 400px;
  }

  .viewport-simulator {
    padding: 1rem;
  }

  .simulator-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .viewport-controls {
    justify-content: center;
  }

  .viewport-range {
    width: 150px;
  }

  .clamp-examples {
    grid-template-columns: 1fr;
  }

  .simulated-viewport {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .fluid-typography-demo {
    padding: 1rem;
    gap: 1.5rem;
    overflow: hidden;
  }

  .viewport-simulator {
    padding: 0.75rem;
    overflow: hidden;
  }

  .viewport-slider {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .viewport-range {
    width: 100%;
    max-width: 100%;
  }

  .viewport-presets {
    justify-content: center;
    gap: 0.25rem;
  }

  .preset-btn {
    padding: 0.3rem 0.6rem;
    font-size: 0.7rem;
  }

  .simulated-viewport {
    padding: 1rem;
    min-height: 150px;
    max-width: calc(100vw - 4rem);
  }

  .viewport-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .viewport-display {
    text-align: center;
    min-width: auto;
  }
}

/* Extra small screens */
@media (max-width: 320px) {
  .fluid-typography-demo {
    padding: 0.75rem;
    gap: 1rem;
  }

  .viewport-simulator {
    padding: 0.5rem;
  }

  .simulated-viewport {
    padding: 0.75rem;
    min-height: 120px;
    max-width: calc(100vw - 3rem);
  }

  .preset-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.65rem;
  }

  .clamp-examples {
    gap: 0.5rem;
  }

  .clamp-example {
    padding: 0.75rem;
  }
}

/* Performance Optimizations */
.simulated-viewport {
  will-change: width;
}

.fluid-heading,
.fluid-subheading,
.fluid-body {
  will-change: font-size;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .simulated-viewport,
  .viewport-range::-webkit-slider-thumb {
    transition: none !important;
  }
}

/* Focus States */
.viewport-range:focus {
  outline: 2px solid var(--neon-green);
  outline-offset: 2px;
}

.preset-btn:focus {
  outline: 2px solid var(--neon-green);
  outline-offset: 2px;
}

.preset-btn:focus:not(:focus-visible) {
  outline: none;
}
