/* ===================================
   SCROLL SNAP TRICK
   ================================= */

/* Scroll Snap Demo Container */
.scroll-snap-demo {
  position: relative;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 400px;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Demo Instructions */
.scroll-instructions {
  text-align: center;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: rgba(255, 0, 128, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(255, 0, 128, 0.2);
}

.scroll-instructions p {
  margin: 0;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.scroll-instructions strong {
  color: var(--neon-pink);
}

/* Gallery Container */
.snap-gallery {
  position: relative;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  padding: 1.5rem;
  overflow: hidden;
}

.gallery-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.gallery-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--neon-pink);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.gallery-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.nav-btn {
  background: transparent;
  border: 1px solid var(--neon-pink);
  color: var(--neon-pink);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  font-family: 'Nunito Sans', sans-serif;
}

.nav-btn:hover:not(:disabled) {
  background: var(--neon-pink);
  color: var(--bg-dark);
  box-shadow: 0 0 15px rgba(255, 0, 128, 0.4);
}

.nav-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

/* Scroll Container */
.snap-container {
  display: flex;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  scroll-behavior: smooth;
  gap: 1rem;
  padding: 1rem 0;
  scrollbar-width: thin;
  scrollbar-color: var(--neon-pink) rgba(255, 255, 255, 0.1);
}

.snap-container::-webkit-scrollbar {
  height: 8px;
}

.snap-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.snap-container::-webkit-scrollbar-thumb {
  background: var(--neon-pink);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.snap-container::-webkit-scrollbar-thumb:hover {
  background: var(--neon-cyan);
}

/* Snap Items */
.snap-item {
  flex: 0 0 280px;
  height: 200px;
  scroll-snap-align: start;
  scroll-snap-stop: always;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, var(--bg-medium), var(--bg-light));
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.snap-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--neon-pink), var(--neon-purple));
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.snap-item:hover::before {
  opacity: 0.1;
}

.snap-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(255, 0, 128, 0.2);
}

.snap-item-content {
  position: relative;
  z-index: 2;
  padding: 1.5rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.snap-item-number {
  font-size: 2rem;
  font-weight: 900;
  color: var(--neon-pink);
  margin-bottom: 0.5rem;
  text-shadow: 0 0 10px rgba(255, 0, 128, 0.5);
}

.snap-item-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.snap-item-description {
  font-size: 0.85rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* Scroll Indicators */
.scroll-indicators {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
  padding: 0.5rem;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator-dot.active {
  background: var(--neon-pink);
  box-shadow: 0 0 10px rgba(255, 0, 128, 0.5);
  transform: scale(1.2);
}

.indicator-dot:hover:not(.active) {
  background: rgba(255, 0, 128, 0.6);
  transform: scale(1.1);
}

/* Snap Types Demo */
.snap-types {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
}

.snap-type-demo {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.snap-type-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--neon-cyan);
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.snap-type-description {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
  line-height: 1.4;
}

.mini-snap-container {
  display: flex;
  overflow-x: auto;
  gap: 0.5rem;
  padding: 0.5rem 0;
  scrollbar-width: thin;
  scrollbar-color: var(--neon-cyan) rgba(255, 255, 255, 0.1);
}

.mini-snap-container::-webkit-scrollbar {
  height: 4px;
}

.mini-snap-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.mini-snap-container::-webkit-scrollbar-thumb {
  background: var(--neon-cyan);
  border-radius: 2px;
}

.mandatory-snap {
  scroll-snap-type: x mandatory;
}

.proximity-snap {
  scroll-snap-type: x proximity;
}

.mini-snap-item {
  flex: 0 0 80px;
  height: 60px;
  background: linear-gradient(135deg, var(--neon-cyan), var(--neon-purple));
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 0.8rem;
  scroll-snap-align: start;
}

/* Responsive Design */
@media (max-width: 768px) {
  .scroll-snap-demo {
    padding: 1.5rem;
    min-height: 350px;
  }

  .snap-gallery {
    padding: 1rem;
  }

  .gallery-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .gallery-controls {
    justify-content: center;
  }

  .snap-item {
    flex: 0 0 250px;
    height: 180px;
  }

  .snap-item-content {
    padding: 1rem;
  }

  .snap-item-number {
    font-size: 1.5rem;
  }

  .snap-item-title {
    font-size: 1rem;
  }

  .snap-types {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .scroll-snap-demo {
    padding: 1rem;
    gap: 1.5rem;
  }

  .snap-item {
    flex: 0 0 220px;
    height: 160px;
  }

  .snap-item-content {
    padding: 0.75rem;
  }

  .snap-item-number {
    font-size: 1.25rem;
  }

  .snap-item-title {
    font-size: 0.9rem;
  }

  .snap-item-description {
    font-size: 0.8rem;
  }
}

/* Performance Optimizations */
.snap-container {
  will-change: scroll-position;
}

.snap-item {
  will-change: transform;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .snap-container {
    scroll-behavior: auto;
  }

  .snap-item,
  .nav-btn,
  .indicator-dot {
    transition: none !important;
  }

  .snap-item:hover {
    transform: none !important;
  }
}

/* Focus States */
.nav-btn:focus {
  outline: 2px solid var(--neon-pink);
  outline-offset: 2px;
}

.indicator-dot:focus {
  outline: 2px solid var(--neon-pink);
  outline-offset: 2px;
}

.snap-item:focus {
  outline: 2px solid var(--neon-pink);
  outline-offset: 2px;
}

.nav-btn:focus:not(:focus-visible),
.indicator-dot:focus:not(:focus-visible),
.snap-item:focus:not(:focus-visible) {
  outline: none;
}
