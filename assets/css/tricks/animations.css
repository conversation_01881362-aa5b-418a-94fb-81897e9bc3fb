/* ===================================
   PHYSICS ANIMATIONS TRICK
   ================================= */

/* Physics Animations Demo Container */
.animations-demo {
  position: relative;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 400px;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Physics Instructions */
.physics-instructions {
  text-align: center;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: rgba(0, 255, 65, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(0, 255, 65, 0.2);
}

.physics-instructions p {
  margin: 0;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.physics-instructions strong {
  color: var(--neon-green);
}

/* Physics Buttons Grid */
.physics-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

/* Base Physics Button */
.physics-button {
  background: linear-gradient(135deg, var(--neon-green), var(--neon-cyan));
  color: var(--text-primary);
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  font-family: 'Nunito Sans', sans-serif;
  min-height: 60px;
}

/* Bounce Animation */
.bounce-btn {
  animation-duration: 0.6s;
  animation-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.bounce-btn:hover {
  animation-name: bounceEffect;
  animation-iteration-count: 1;
}

@keyframes bounceEffect {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translateY(0);
  }
  40%,
  43% {
    transform: translateY(-20px);
  }
  70% {
    transform: translateY(-10px);
  }
  90% {
    transform: translateY(-5px);
  }
}

/* Spring Animation */
.spring-btn {
  transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.spring-btn:hover {
  transform: scale(1.1) rotate(2deg);
  animation: springPulse 0.6s ease-in-out;
}

@keyframes springPulse {
  0% {
    transform: scale(1.1) rotate(2deg);
  }
  50% {
    transform: scale(1.15) rotate(-1deg);
  }
  100% {
    transform: scale(1.1) rotate(2deg);
  }
}

/* Elastic Animation */
.elastic-btn {
  transition: all 0.5s cubic-bezier(0.68, -0.6, 0.32, 1.6);
}

.elastic-btn:hover {
  transform: scale(1.05);
  animation: elasticWave 0.8s ease-in-out;
}

@keyframes elasticWave {
  0% {
    transform: scale(1.05) skewX(0deg);
  }
  25% {
    transform: scale(1.1) skewX(5deg);
  }
  50% {
    transform: scale(1.08) skewX(-3deg);
  }
  75% {
    transform: scale(1.06) skewX(2deg);
  }
  100% {
    transform: scale(1.05) skewX(0deg);
  }
}

/* Gravity Animation */
.gravity-btn {
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.gravity-btn:hover {
  animation: gravityDrop 1s ease-in-out;
}

@keyframes gravityDrop {
  0% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-30px);
  }
  60% {
    transform: translateY(10px);
  }
  80% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0);
  }
}

/* Physics Playground */
.physics-playground {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
  min-height: 200px;
  overflow: hidden;
}

.playground-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--neon-green);
  margin-bottom: 1rem;
  text-align: center;
}

/* Floating Particles */
.particle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: var(--neon-cyan);
  border-radius: 50%;
  animation: floatParticle 3s ease-in-out infinite;
  box-shadow: 0 0 10px var(--neon-cyan);
}

.particle:nth-child(2) {
  animation-delay: 0.5s;
  left: 20%;
}
.particle:nth-child(3) {
  animation-delay: 1s;
  left: 40%;
}
.particle:nth-child(4) {
  animation-delay: 1.5s;
  left: 60%;
}
.particle:nth-child(5) {
  animation-delay: 2s;
  left: 80%;
}

@keyframes floatParticle {
  0%,
  100% {
    transform: translateY(150px) scale(0.8);
    opacity: 0;
  }
  50% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .physics-buttons {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .physics-button {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }

  .animations-demo {
    padding: 1.5rem;
  }
}
