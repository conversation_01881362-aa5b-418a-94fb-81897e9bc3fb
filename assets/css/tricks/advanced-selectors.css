/* ===================================
   ADVANCED SELECTORS TRICK
   ================================= */

/* Advanced Selectors Demo Container */
.advanced-selectors-demo {
  position: relative;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 400px;
}

/* Selectors Instructions */
.selectors-instructions {
  text-align: center;
  margin-bottom: 2rem;
  padding: 0.75rem;
  background: rgba(255, 0, 128, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(255, 0, 128, 0.2);
}

.selectors-instructions p {
  margin: 0;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.selectors-instructions strong {
  color: var(--neon-pink);
}

/* Smart Form */
.smart-form {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  padding: 2rem;
  border: 2px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  max-width: 500px;
  margin: 0 auto;
}

/* Form has valid inputs - green border */
.smart-form:has(input:valid:not(:placeholder-shown)) {
  border-color: var(--neon-green);
  box-shadow: 0 0 20px rgba(0, 255, 65, 0.2);
}

/* Form has invalid inputs - red border */
.smart-form:has(input:invalid:not(:placeholder-shown)) {
  border-color: var(--neon-pink);
  box-shadow: 0 0 20px rgba(255, 0, 128, 0.2);
}

/* Form has focus - cyan border */
.smart-form:has(input:focus) {
  border-color: var(--neon-cyan);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.2);
}

.form-group {
  margin-bottom: 1.5rem;
  position: relative;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
  font-weight: 600;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.form-input {
  width: 100%;
  padding: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.3);
  color: var(--text-primary);
  font-size: 1rem;
  font-family: 'Nunito Sans', sans-serif;
  transition: all 0.3s ease;
}

.form-input::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

/* Input states using modern selectors */
.form-input:is(:focus, :hover) {
  outline: none;
  border-color: var(--neon-cyan);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

.form-input:valid:not(:placeholder-shown) {
  border-color: var(--neon-green);
  background: rgba(0, 255, 65, 0.05);
}

.form-input:invalid:not(:placeholder-shown) {
  border-color: var(--neon-pink);
  background: rgba(255, 0, 128, 0.05);
}

/* Label changes color when input is focused */
.form-group:has(input:focus) .form-label {
  color: var(--neon-cyan);
}

.form-group:has(input:valid:not(:placeholder-shown)) .form-label {
  color: var(--neon-green);
}

.form-group:has(input:invalid:not(:placeholder-shown)) .form-label {
  color: var(--neon-pink);
}

/* Status indicators */
.status-indicator {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.form-group:has(input:valid:not(:placeholder-shown)) .status-indicator.valid {
  opacity: 1;
}

.form-group:has(input:invalid:not(:placeholder-shown))
  .status-indicator.invalid {
  opacity: 1;
}

/* Submit button states */
.submit-btn {
  width: 100%;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, var(--neon-purple), var(--neon-pink));
  color: var(--text-primary);
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0.5;
  pointer-events: none;
  font-family: 'Nunito Sans', sans-serif;
}

/* Enable button when form has all valid inputs */
.smart-form:has(input:valid:not(:placeholder-shown)):not(:has(input:invalid))
  .submit-btn {
  opacity: 1;
  pointer-events: auto;
  background: linear-gradient(135deg, var(--neon-green), var(--neon-cyan));
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 255, 65, 0.3);
}

/* Selector Examples */
.selector-examples {
  margin-top: 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.selector-example {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.selector-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--neon-cyan);
  margin-bottom: 0.5rem;
}

.selector-code {
  font-family: monospace;
  font-size: 0.8rem;
  color: var(--text-secondary);
  background: rgba(0, 0, 0, 0.3);
  padding: 0.5rem;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.selector-description {
  font-size: 0.8rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
  .advanced-selectors-demo {
    padding: 1.5rem;
  }

  .smart-form {
    padding: 1.5rem;
  }

  .selector-examples {
    grid-template-columns: 1fr;
  }
}
