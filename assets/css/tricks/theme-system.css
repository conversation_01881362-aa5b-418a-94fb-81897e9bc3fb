/* ===================================
   DYNAMIC THEMES TRICK
   ================================= */

/* Theme System Demo Container */
.theme-system-demo {
  position: relative;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 400px;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  transition: all 0.3s ease;
}

/* Theme Instructions */
.theme-instructions {
  text-align: center;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.theme-instructions p {
  margin: 0;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.theme-instructions strong {
  color: var(--neon-purple);
}

/* Theme Switcher */
.theme-switcher {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.theme-btn {
  background: transparent;
  border: 2px solid var(--neon-purple);
  color: var(--neon-purple);
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Nunito Sans', sans-serif;
  font-weight: 600;
  font-size: 0.9rem;
  min-width: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.theme-btn:hover {
  background: var(--neon-purple);
  color: var(--bg-dark);
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.4);
  transform: translateY(-2px);
}

.theme-btn.active {
  background: var(--neon-purple);
  color: var(--bg-dark);
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.6);
}

/* Theme Preview */
.theme-preview {
  background: var(--theme-bg, rgba(255, 255, 255, 0.02));
  border: 1px solid var(--theme-border, rgba(255, 255, 255, 0.1));
  border-radius: 12px;
  padding: 2rem;
  transition: all 0.3s ease;
  color: var(--theme-text, var(--text-primary));
}

.preview-content {
  text-align: center;
}

.preview-title {
  font-size: 2rem;
  font-weight: 900;
  margin-bottom: 1rem;
  color: var(--theme-accent, var(--neon-purple));
  text-shadow: 0 0 10px var(--theme-glow, rgba(139, 92, 246, 0.5));
}

.preview-subtitle {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--theme-secondary, var(--neon-cyan));
}

.preview-text {
  font-size: 1rem;
  line-height: 1.6;
  color: var(--theme-text, var(--text-primary));
  max-width: 600px;
  margin: 0 auto 2rem;
}

.preview-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.preview-btn {
  background: var(--theme-accent, var(--neon-purple));
  color: var(--theme-btn-text, var(--bg-dark));
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Nunito Sans', sans-serif;
  font-weight: 600;
}

.preview-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--theme-shadow, rgba(139, 92, 246, 0.3));
}

/* Theme Variants */
[data-demo-theme='dark'] {
  --theme-bg: rgba(0, 0, 0, 0.8);
  --theme-border: rgba(255, 255, 255, 0.2);
  --theme-text: #ffffff;
  --theme-accent: #8b5cf6;
  --theme-secondary: #00ffff;
  --theme-btn-text: #000000;
  --theme-glow: rgba(139, 92, 246, 0.5);
  --theme-shadow: rgba(139, 92, 246, 0.3);
}

[data-demo-theme='light'] {
  --theme-bg: rgba(255, 255, 255, 0.95);
  --theme-border: rgba(0, 0, 0, 0.1);
  --theme-text: #1a1a1a;
  --theme-accent: #6366f1;
  --theme-secondary: #0891b2;
  --theme-btn-text: #ffffff;
  --theme-glow: rgba(99, 102, 241, 0.3);
  --theme-shadow: rgba(99, 102, 241, 0.2);
}

[data-demo-theme='neon'] {
  --theme-bg: rgba(0, 0, 0, 0.9);
  --theme-border: rgba(0, 255, 255, 0.3);
  --theme-text: #00ffff;
  --theme-accent: #ff0080;
  --theme-secondary: #00ff41;
  --theme-btn-text: #000000;
  --theme-glow: rgba(255, 0, 128, 0.8);
  --theme-shadow: rgba(255, 0, 128, 0.4);
}

/* CSS Variables Display */
.variables-display {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.variables-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--neon-cyan);
  margin-bottom: 1rem;
  text-align: center;
}

.variables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.variable-item {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 6px;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.variable-name {
  font-family: monospace;
  font-size: 0.8rem;
  color: var(--neon-green);
  margin-bottom: 0.5rem;
}

.variable-value {
  font-family: monospace;
  font-size: 0.8rem;
  color: var(--text-secondary);
  background: rgba(0, 0, 0, 0.3);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}
