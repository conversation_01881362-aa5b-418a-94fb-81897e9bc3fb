/* ===================================
   CSS MAGIC TRICKS - MAIN STYLES
   ================================= */

/* Reset e Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Nunito Sans', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--bg-dark);
  overflow-x: hidden;
}

/* Layout Principal */
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header Hero */
.hero-header {
  text-align: center;
  padding: 4rem 2rem;
  background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-medium) 100%);
  position: relative;
  overflow: hidden;
}

.hero-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at 50% 50%,
    rgba(0, 255, 255, 0.1) 0%,
    transparent 70%
  );
  pointer-events: none;
}

.neon-title {
  font-size: clamp(2.5rem, 8vw, 5rem);
  font-weight: 900;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: 1rem;
  position: relative;
  z-index: 1;
}

.hero-description {
  font-size: clamp(1.1rem, 3vw, 1.5rem);
  color: var(--text-secondary);
  margin-bottom: 2rem;
  position: relative;
  z-index: 1;
}

/* Grid de Cards */
.cards-grid {
  flex: 1;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* Card Base Styles */
.magic-card {
  background: linear-gradient(145deg, var(--bg-medium), var(--bg-light))
    padding-box;
  border: 2px solid transparent;
  border-radius: 16px;
  padding: 2rem;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  margin-bottom: 2rem;
}

.magic-card::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(
    45deg,
    var(--neon-cyan),
    var(--neon-purple),
    var(--neon-pink)
  );
  border-radius: 16px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.magic-card:hover::before {
  opacity: 0.7;
  animation: neonPulse 2s ease-in-out infinite;
}

/* Card Header */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.trick-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

.trick-number {
  background: var(--neon-cyan);
  color: var(--bg-dark);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-weight: 700;
  font-size: 0.9rem;
}

/* Card Demo Area */
.card-demo {
  min-height: 200px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Card Controls */
.card-controls {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

/* Footer */
.site-footer {
  text-align: center;
  padding: 2rem;
  background: var(--bg-medium);
  color: var(--text-secondary);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive Design */
@media (min-width: 768px) {
  .cards-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1024px) {
  .cards-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus Styles */
*:focus {
  outline: 2px solid var(--neon-cyan);
  outline-offset: 2px;
}

/* Utility Classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
