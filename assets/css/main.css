/* ===================================
   CSS MAGIC TRICKS - MAIN STYLES
   ================================= */

/* Reset e Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Nunito Sans', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--bg-dark);
  overflow-x: hidden;
}

/* Layout Principal */
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header Hero */
.hero-header {
  text-align: center;
  padding: 4rem 2rem;
  background: var(--gradient-bg);
  position: relative;
  overflow: hidden;
}

.hero-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at 50% 50%,
    rgba(0, 255, 255, 0.1) 0%,
    transparent 70%
  );
  pointer-events: none;
}

.neon-title {
  font-size: clamp(2.5rem, 8vw, 5rem);
  font-weight: 900;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: 1rem;
  position: relative;
  z-index: 1;
}

.hero-description {
  font-size: clamp(1.1rem, 3vw, 1.5rem);
  color: var(--text-secondary);
  margin-bottom: 2rem;
  position: relative;
  z-index: 1;
}

/* Single Column Layout for Cards */
.cards-grid {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

/* Enhanced Card Base Styles - Single Column Layout */
.magic-card {
  background: var(--gradient-card) padding-box;
  border: 2px solid transparent;
  border-radius: var(--border-radius);
  padding: var(--spacing-xl);
  position: relative;
  overflow: hidden;
  transition: all var(--animation-speed-fast) ease;
  will-change: transform;
  contain: layout style paint;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.magic-card::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: var(--gradient-border);
  border-radius: var(--border-radius);
  opacity: 0;
  transition: opacity var(--animation-speed-fast) ease;
  z-index: -1;
}

.magic-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-border);
  border-radius: var(--border-radius);
  opacity: 0;
  transition: opacity var(--animation-speed-fast) ease;
  z-index: -2;
  filter: blur(20px);
}

.magic-card:hover::before {
  opacity: 0.4;
  animation: neonPulseGentle var(--animation-speed-slow) ease-in-out infinite;
}

.magic-card:hover::after {
  opacity: 0.15;
  animation: cardGlowGentle var(--animation-speed-slow) ease-in-out infinite
    alternate;
}

.magic-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 255, 255, 0.1);
}

/* Card Header */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.trick-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

.trick-number {
  background: var(--neon-cyan);
  color: var(--bg-dark);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-weight: 700;
  font-size: 0.9rem;
}

/* Card Demo Area - Enhanced for Single Column */
.card-demo {
  min-height: 300px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  max-width: 100%;
  box-sizing: border-box;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Card Controls */
.card-controls {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

/* Demo Placeholders */
.demo-description {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 1rem;
  font-style: italic;
}

.placeholder-demo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  text-align: center;
}

.demo-box,
.demo-button,
.theme-toggle,
.physics-button {
  background: var(--neon-cyan);
  color: var(--bg-dark);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Nunito Sans', sans-serif;
}

.demo-box:hover,
.demo-button:hover,
.theme-toggle:hover,
.physics-button:hover {
  background: var(--neon-purple);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.fluid-text {
  font-size: clamp(1.2rem, 3vw, 2rem);
  color: var(--neon-cyan);
  font-weight: 700;
  text-align: center;
}

.scroll-container {
  display: flex;
  gap: 1rem;
  overflow-x: auto;
  padding: 1rem 0;
  scroll-snap-type: x mandatory;
}

.scroll-item {
  flex: 0 0 120px;
  height: 80px;
  background: var(--neon-purple);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  scroll-snap-align: start;
  font-weight: 600;
}

.grid-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto;
  gap: 0.5rem;
  max-width: 200px;
  margin: 0 auto;
}

.grid-item {
  background: var(--neon-green);
  color: var(--bg-dark);
  padding: 0.5rem;
  text-align: center;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
}

.houdini-canvas {
  width: 150px;
  height: 100px;
  background: linear-gradient(45deg, var(--neon-cyan), var(--neon-pink));
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 0.9rem;
}

input[type='email'],
input[type='password'],
input[type='range'] {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--text-primary);
  padding: 0.5rem;
  border-radius: 4px;
  font-family: 'Nunito Sans', sans-serif;
}

input[type='email']:focus,
input[type='password']:focus {
  outline: none;
  border-color: var(--neon-cyan);
  box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.2);
}

/* Footer */
.site-footer {
  background: var(--bg-medium);
  color: var(--text-secondary);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 2rem;
}

.footer-content {
  max-width: 1400px;
  margin: 0 auto;
  text-align: center;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.footer-links a {
  color: var(--neon-cyan);
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 4px;
}

.footer-links a:hover {
  color: var(--text-primary);
  background: rgba(0, 255, 255, 0.1);
  transform: translateY(-2px);
}

/* Responsive Design - Single Column Layout */
/* Mobile First - Extra Small Screens */
@media (max-width: 480px) {
  .cards-grid {
    padding: 1rem;
    gap: 2rem;
  }

  .magic-card {
    padding: 1.5rem;
    margin: 0;
  }

  .card-demo {
    padding: 1rem;
    min-height: 250px;
  }

  .card-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .trick-title {
    font-size: 1.25rem;
  }

  .trick-number {
    align-self: center;
  }
}

/* Small Screens */
@media (min-width: 481px) and (max-width: 767px) {
  .cards-grid {
    padding: 1.5rem;
    gap: 2.5rem;
  }

  .magic-card {
    padding: 2rem;
  }

  .card-demo {
    padding: 1.5rem;
  }
}

/* Medium Screens and Up */
@media (min-width: 768px) {
  .cards-grid {
    padding: 3rem 4rem;
    gap: 4rem;
  }

  .magic-card {
    padding: 3rem;
  }
}

@media (min-width: 1280px) {
  .cards-grid {
    padding: 4rem 6rem;
    gap: 5rem;
  }

  .magic-card {
    padding: 4rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus Styles */
*:focus {
  outline: 2px solid var(--neon-cyan);
  outline-offset: 2px;
}

/* Utility Classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Global Overflow Prevention */
* {
  box-sizing: border-box;
}

html {
  overflow-x: hidden;
}

body {
  overflow-x: hidden;
  max-width: 100vw;
}

/* Prevent horizontal scroll on all containers */
.cards-grid,
.magic-card,
.card-demo,
.card-controls,
.placeholder-demo {
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* Text overflow handling */
h1,
h2,
h3,
h4,
h5,
h6,
p,
span,
div {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* Button and input responsiveness */
button,
input,
select,
textarea {
  max-width: 100%;
  box-sizing: border-box;
}

/* Image responsiveness */
img,
video,
iframe {
  max-width: 100%;
  height: auto;
}

/* Flex container overflow prevention */
.flex-container {
  overflow: hidden;
}

/* Grid container overflow prevention */
.grid-container {
  overflow: hidden;
}

/* Code block responsiveness */
pre,
code {
  max-width: 100%;
  overflow-x: auto;
  word-wrap: break-word;
  white-space: pre-wrap;
}

/* Table responsiveness */
table {
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
  display: block;
  white-space: nowrap;
}

@media (max-width: 768px) {
  table {
    font-size: 0.8rem;
  }
}

/* Enhanced mobile-first responsive utilities */
@media (max-width: 320px) {
  .cards-grid {
    padding: 0.5rem;
    gap: 1rem;
  }

  .magic-card {
    padding: 1rem;
    margin: 0;
  }

  .card-demo {
    padding: 0.75rem;
    min-height: 200px;
  }

  .card-header {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .trick-title {
    font-size: 1.1rem;
  }

  .trick-number {
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
  }
}
