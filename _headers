# Global headers for all routes
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=(), interest-cohort=()

# Service Worker
/sw.js
  Service-Worker-Allowed: /
  Cache-Control: public, max-age=0, must-revalidate

# PWA Manifest
/manifest.json
  Content-Type: application/manifest+json; charset=utf-8
  Cache-Control: public, max-age=31536000, immutable

# Browser Config
/browserconfig.xml
  Content-Type: application/xml; charset=utf-8
  Cache-Control: public, max-age=31536000, immutable

# CSS Files
/assets/css/*
  Content-Type: text/css; charset=utf-8
  Cache-Control: public, max-age=31536000, immutable

# JavaScript Files
/assets/js/*
  Content-Type: application/javascript; charset=utf-8
  Cache-Control: public, max-age=31536000, immutable

# Images
/assets/images/*
  Cache-Control: public, max-age=31536000, immutable

# Fonts
*.woff
  Cache-Control: public, max-age=31536000, immutable
*.woff2
  Cache-Control: public, max-age=31536000, immutable
*.ttf
  Cache-Control: public, max-age=31536000, immutable
*.eot
  Cache-Control: public, max-age=31536000, immutable

# Icons and favicons
*.ico
  Cache-Control: public, max-age=31536000, immutable
*.png
  Cache-Control: public, max-age=31536000, immutable
*.jpg
  Cache-Control: public, max-age=31536000, immutable
*.jpeg
  Cache-Control: public, max-age=31536000, immutable
*.gif
  Cache-Control: public, max-age=31536000, immutable
*.svg
  Cache-Control: public, max-age=31536000, immutable
*.webp
  Cache-Control: public, max-age=31536000, immutable

# HTML files (no cache for dynamic content)
/*.html
  Cache-Control: public, max-age=0, must-revalidate
