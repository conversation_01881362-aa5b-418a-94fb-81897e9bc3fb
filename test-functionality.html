<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Functionality Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 2rem;
            background: #1a1a2e;
            color: white;
        }
        .test-section {
            margin: 2rem 0;
            padding: 1rem;
            border: 1px solid #00ffff;
            border-radius: 8px;
        }
        .test-button {
            background: #00ffff;
            color: #000;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin: 0.5rem;
        }
        .test-result {
            margin-top: 1rem;
            padding: 0.5rem;
            background: rgba(0, 255, 255, 0.1);
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🧪 JavaScript Functionality Test</h1>
    
    <div class="test-section">
        <h2>1. Main.js Functions Test</h2>
        <button class="test-button" onclick="testMainFunctions()">Test Main Functions</button>
        <div id="main-result" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>2. Code Toggle Test</h2>
        <button class="test-button" onclick="testCodeToggle()">Test Code Toggle</button>
        <div id="toggle-result" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>3. Clipboard Test</h2>
        <button class="test-button" onclick="testClipboard()">Test Clipboard</button>
        <div id="clipboard-result" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>4. Error Handling Test</h2>
        <button class="test-button" onclick="testErrorHandling()">Test Error Handling</button>
        <div id="error-result" class="test-result"></div>
    </div>

    <script>
        function testMainFunctions() {
            const result = document.getElementById('main-result');
            let tests = [];

            // Test if MagicTricks object exists
            if (typeof window.MagicTricks !== 'undefined') {
                tests.push('✅ MagicTricks object loaded');
                
                // Test individual functions
                const functions = ['toggleCodePanel', 'copyToClipboard', 'announceToScreenReader', 'debounce'];
                functions.forEach(func => {
                    if (typeof window.MagicTricks[func] === 'function') {
                        tests.push(`✅ ${func} function available`);
                    } else {
                        tests.push(`❌ ${func} function missing`);
                    }
                });
            } else {
                tests.push('❌ MagicTricks object not loaded');
            }

            result.innerHTML = tests.join('<br>');
        }

        function testCodeToggle() {
            const result = document.getElementById('toggle-result');
            let tests = [];

            // Test if CodeToggle class exists
            if (typeof window.codeToggle !== 'undefined') {
                tests.push('✅ CodeToggle instance loaded');
                
                // Test methods
                const methods = ['closeAllPanels', 'openPanel', 'getPanelStats'];
                methods.forEach(method => {
                    if (typeof window.codeToggle[method] === 'function') {
                        tests.push(`✅ ${method} method available`);
                    } else {
                        tests.push(`❌ ${method} method missing`);
                    }
                });

                // Test stats
                try {
                    const stats = window.codeToggle.getPanelStats();
                    tests.push(`✅ Panel stats: ${JSON.stringify(stats)}`);
                } catch (e) {
                    tests.push(`❌ Error getting stats: ${e.message}`);
                }
            } else {
                tests.push('❌ CodeToggle instance not loaded');
            }

            result.innerHTML = tests.join('<br>');
        }

        function testClipboard() {
            const result = document.getElementById('clipboard-result');
            let tests = [];

            // Test if ClipboardManager exists
            if (typeof window.clipboardManager !== 'undefined') {
                tests.push('✅ ClipboardManager instance loaded');
                
                // Test methods
                const methods = ['copyText', 'copyElementContent', 'getCopyStats'];
                methods.forEach(method => {
                    if (typeof window.clipboardManager[method] === 'function') {
                        tests.push(`✅ ${method} method available`);
                    } else {
                        tests.push(`❌ ${method} method missing`);
                    }
                });

                // Test clipboard API availability
                if (navigator.clipboard) {
                    tests.push('✅ Modern Clipboard API available');
                } else {
                    tests.push('⚠️ Using fallback clipboard method');
                }

                // Test stats
                try {
                    const stats = window.clipboardManager.getCopyStats();
                    tests.push(`✅ Copy stats: ${JSON.stringify(stats)}`);
                } catch (e) {
                    tests.push(`❌ Error getting stats: ${e.message}`);
                }
            } else {
                tests.push('❌ ClipboardManager instance not loaded');
            }

            result.innerHTML = tests.join('<br>');
        }

        function testErrorHandling() {
            const result = document.getElementById('error-result');
            let tests = [];

            // Test error handling function
            if (typeof window.MagicTricks !== 'undefined' && typeof window.MagicTricks.handleError === 'function') {
                tests.push('✅ Error handling function available');
                
                // Test error handling (this will show an error toast)
                try {
                    window.MagicTricks.handleError(new Error('Test error'), 'Test Context');
                    tests.push('✅ Error handling executed (check for toast message)');
                } catch (e) {
                    tests.push(`❌ Error in error handling: ${e.message}`);
                }
            } else {
                tests.push('❌ Error handling function not available');
            }

            // Test global error listeners
            tests.push('✅ Global error listeners should be active');

            result.innerHTML = tests.join('<br>');
        }

        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🧪 Running automatic functionality tests...');
                testMainFunctions();
                testCodeToggle();
                testClipboard();
                console.log('🧪 Tests completed - check results on page');
            }, 2000);
        });
    </script>
</body>
</html>
