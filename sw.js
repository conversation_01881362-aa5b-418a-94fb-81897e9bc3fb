/* ===================================
   SERVICE WORKER - CSS MAGIC TRICKS
   ================================= */

const CACHE_NAME = 'css-magic-tricks-v1.0.0';
const OFFLINE_PAGE = '/offline.html';

// Resources to cache immediately
const STATIC_CACHE_URLS = [
  '/',
  '/index.html',
  '/favicon.svg',
  '/manifest.json',
  '/assets/css/main.css',
  '/assets/css/neon-theme.css',
  '/assets/css/tricks/container-queries.css',
  '/assets/css/tricks/tooltips.css',
  '/assets/css/tricks/hover-effects.css',
  '/assets/css/tricks/fluid-typography.css',
  '/assets/css/tricks/scroll-snap.css',
  '/assets/css/tricks/theme-system.css',
  '/assets/css/tricks/grid-layouts.css',
  '/assets/css/tricks/animations.css',
  '/assets/css/tricks/advanced-selectors.css',
  '/assets/css/tricks/houdini.css',
  '/assets/js/main.js',
  '/assets/js/copy-clipboard.js',
  '/assets/js/code-toggle.js',
  '/assets/js/image-optimization.js',
  '/assets/js/fluid-typography.js',
  'https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@200;300;400;500;600;700;800;900&display=swap',
  'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css',
  'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js',
  'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js',
  'https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.11/clipboard.min.js',
];

// Install event - cache static resources
self.addEventListener('install', (event) => {
  console.log('🔧 Service Worker installing...');

  event.waitUntil(
    caches
      .open(CACHE_NAME)
      .then((cache) => {
        console.log('📦 Caching static resources...');
        return cache.addAll(STATIC_CACHE_URLS);
      })
      .then(() => {
        console.log('✅ Static resources cached successfully');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('❌ Failed to cache static resources:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('🚀 Service Worker activating...');

  event.waitUntil(
    caches
      .keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME) {
              console.log('🗑️ Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('✅ Service Worker activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Skip chrome-extension and other non-http(s) requests
  if (!url.protocol.startsWith('http')) {
    return;
  }

  event.respondWith(handleRequest(request));
});

async function handleRequest(request) {
  const url = new URL(request.url);

  try {
    // Strategy 1: Cache First for static assets
    if (isStaticAsset(url)) {
      return await cacheFirst(request);
    }

    // Strategy 2: Network First for HTML pages
    if (isHTMLPage(url)) {
      return await networkFirst(request);
    }

    // Strategy 3: Stale While Revalidate for external resources
    if (isExternalResource(url)) {
      return await staleWhileRevalidate(request);
    }

    // Default: Network First
    return await networkFirst(request);
  } catch (error) {
    console.error('❌ Request failed:', error);
    return await handleOffline(request);
  }
}

// Cache First Strategy - for static assets
async function cacheFirst(request) {
  const cachedResponse = await caches.match(request);

  if (cachedResponse) {
    return cachedResponse;
  }

  try {
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  } catch (error) {
    throw error;
  }
}

// Network First Strategy - for HTML pages
async function networkFirst(request) {
  try {
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  } catch (error) {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    throw error;
  }
}

// Stale While Revalidate Strategy - for external resources
async function staleWhileRevalidate(request) {
  const cachedResponse = await caches.match(request);

  const fetchPromise = fetch(request)
    .then((networkResponse) => {
      if (networkResponse.ok) {
        const cache = caches.open(CACHE_NAME);
        cache.then((c) => c.put(request, networkResponse.clone()));
      }
      return networkResponse;
    })
    .catch(() => {
      // Silently fail for external resources
      return cachedResponse;
    });

  return cachedResponse || fetchPromise;
}

// Handle offline scenarios
async function handleOffline(request) {
  const url = new URL(request.url);

  // For HTML pages, return offline page
  if (isHTMLPage(url)) {
    const offlineResponse = await caches.match(OFFLINE_PAGE);
    if (offlineResponse) {
      return offlineResponse;
    }

    // Fallback offline response
    return new Response(
      `<!DOCTYPE html>
      <html>
        <head>
          <title>Offline - CSS Magic Tricks</title>
          <style>
            body { 
              font-family: 'Nunito Sans', sans-serif; 
              background: #0a0a0a; 
              color: #fff; 
              text-align: center; 
              padding: 2rem; 
            }
            .offline-message {
              max-width: 400px;
              margin: 2rem auto;
              padding: 2rem;
              border: 1px solid #00ffff;
              border-radius: 8px;
            }
          </style>
        </head>
        <body>
          <div class="offline-message">
            <h1>🔌 You're Offline</h1>
            <p>CSS Magic Tricks is not available right now. Please check your connection and try again.</p>
            <button onclick="window.location.reload()">Try Again</button>
          </div>
        </body>
      </html>`,
      {
        status: 200,
        headers: { 'Content-Type': 'text/html' },
      }
    );
  }

  // For other resources, return cached version or fail gracefully
  const cachedResponse = await caches.match(request);
  return (
    cachedResponse ||
    new Response('Resource not available offline', { status: 503 })
  );
}

// Helper functions
function isStaticAsset(url) {
  return url.pathname.match(
    /\.(css|js|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot)$/
  );
}

function isHTMLPage(url) {
  return (
    url.pathname.endsWith('/') ||
    url.pathname.endsWith('.html') ||
    !url.pathname.includes('.')
  );
}

function isExternalResource(url) {
  return (
    !url.hostname.includes(self.location.hostname) &&
    (url.hostname.includes('googleapis.com') ||
      url.hostname.includes('cdnjs.cloudflare.com'))
  );
}

// Background sync for analytics (if needed)
self.addEventListener('sync', (event) => {
  if (event.tag === 'analytics-sync') {
    event.waitUntil(syncAnalytics());
  }
});

async function syncAnalytics() {
  // Implement analytics sync when back online
  console.log('📊 Syncing analytics data...');
}

// Push notifications (for future use)
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json();

    event.waitUntil(
      self.registration.showNotification(data.title, {
        body: data.body,
        icon: '/icon-192x192.png',
        badge: '/badge-72x72.png',
        tag: 'css-magic-tricks',
      })
    );
  }
});

console.log('🎨 CSS Magic Tricks Service Worker loaded');
