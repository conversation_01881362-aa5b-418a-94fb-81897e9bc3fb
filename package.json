{"name": "css-magic-tricks", "version": "1.0.0", "description": "Interactive demonstrations of 10 advanced CSS techniques with neon cyberpunk theme", "main": "index.html", "scripts": {"dev": "python -m http.server 3000 || python3 -m http.server 3000 || npx serve .", "start": "python -m http.server 8080 || python3 -m http.server 8080 || npx serve .", "build": "echo 'No build step required - static files'", "preview": "npx serve . -p 4173", "lint": "echo '<PERSON><PERSON> not configured'", "test": "echo 'No tests configured'"}, "keywords": ["css", "css-tricks", "web-development", "frontend", "responsive-design", "css-grid", "css-animations", "container-queries", "css-<PERSON><PERSON><PERSON>", "fluid-typography", "scroll-snap", "hover-effects", "pwa", "service-worker", "mobile-first", "cyberpunk", "neon-theme"], "author": {"name": "Blueprint Blog", "url": "https://blueprintblog.tech"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/blueprintblog/css-magic-tricks"}, "homepage": "https://css-magic-tricks.vercel.app", "bugs": {"url": "https://github.com/blueprintblog/css-magic-tricks/issues"}, "engines": {"node": ">=14.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "devDependencies": {}, "dependencies": {}, "files": ["index.html", "manifest.json", "sw.js", "browserconfig.xml", "robots.txt", "sitemap.xml", "assets/", "LICENSE", "README.md"], "vercel": {"framework": null, "buildCommand": null, "outputDirectory": ".", "installCommand": null, "devCommand": null}}